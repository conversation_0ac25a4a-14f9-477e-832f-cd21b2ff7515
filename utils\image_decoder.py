"""
Utility script to decode and save base64 image data from gpt-image-1.
"""
import base64
import os
from datetime import datetime
import arg<PERSON><PERSON>

def decode_and_save_image(base64_data: str, output_filename: str = None, output_dir: str = "generated_images") -> str:
    """
    Decode base64 image data and save it to a file.
    
    Args:
        base64_data: The base64 encoded image string
        output_filename: Optional custom filename
        output_dir: Directory to save the image (default: "generated_images")
        
    Returns:
        str: Path to the saved image file
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate filename if not provided
    if not output_filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"generated_image_{timestamp}.png"
    
    # Ensure filename has correct extension
    if not output_filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):
        output_filename += '.png'
    
    # Full path
    output_path = os.path.join(output_dir, output_filename)
    
    try:
        # Decode base64 data
        image_data = base64.b64decode(base64_data)
        
        # Save to file
        with open(output_path, 'wb') as f:
            f.write(image_data)
        
        print(f"✅ Image successfully saved to: {output_path}")
        print(f"📁 File size: {len(image_data):,} bytes ({len(image_data)/1024/1024:.2f} MB)")
        
        return output_path
        
    except Exception as e:
        print(f"❌ Error saving image: {str(e)}")
        return None

def decode_from_data_url(data_url: str, output_filename: str = None, output_dir: str = "generated_images") -> str:
    """
    Decode image from a data URL format (data:image/png;base64,...)
    
    Args:
        data_url: The data URL string
        output_filename: Optional custom filename
        output_dir: Directory to save the image
        
    Returns:
        str: Path to the saved image file
    """
    try:
        # Extract base64 data from data URL
        if "data:image" in data_url and "base64," in data_url:
            base64_data = data_url.split("base64,", 1)[1]
        else:
            # Assume it's already just base64 data
            base64_data = data_url
        
        return decode_and_save_image(base64_data, output_filename, output_dir)
        
    except Exception as e:
        print(f"❌ Error processing data URL: {str(e)}")
        return None

def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description="Decode and save base64 image data")
    parser.add_argument("base64_data", help="Base64 encoded image data or data URL")
    parser.add_argument("-o", "--output", help="Output filename")
    parser.add_argument("-d", "--dir", default="generated_images", help="Output directory")
    
    args = parser.parse_args()
    
    # Detect if it's a data URL or raw base64
    if args.base64_data.startswith("data:image"):
        decode_from_data_url(args.base64_data, args.output, args.dir)
    else:
        decode_and_save_image(args.base64_data, args.output, args.dir)

if __name__ == "__main__":
    main()