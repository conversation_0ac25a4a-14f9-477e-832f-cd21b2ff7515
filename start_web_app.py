"""
Simple startup script for the web application.
"""
import os
import sys
import subprocess
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def main():
    """Start the web application."""
    print("🚀 Starting Langchain Image Generation Agent Web Interface...")
    print("=" * 60)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ Error: .env file not found!")
        print("Please create a .env file with your OPENAI_API_KEY")
        return False
    
    # Check if templates directory exists
    if not os.path.exists('templates'):
        print("❌ Error: templates directory not found!")
        return False
    
    # Create necessary directories
    os.makedirs('generated_images', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("✅ Environment check passed")
    print("🌐 Starting web server on http://localhost:5000")
    print("📁 Generated images will be saved to: generated_images/")
    print("")
    print("💡 Open your browser and navigate to: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        # Start the Flask application
        os.system("python web_app.py")
    except KeyboardInterrupt:
        print("\n👋 Server stopped. Goodbye!")
        return True
    except Exception as e:
        print(f"\n❌ Error starting server: {str(e)}")
        return False

if __name__ == "__main__":
    main()