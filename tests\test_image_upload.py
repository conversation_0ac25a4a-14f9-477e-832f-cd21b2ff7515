#!/usr/bin/env python3
"""
Test script to simulate image upload functionality and identify issues.
"""

import requests
import base64
import json
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_image():
    """Load the actual test image for upload testing."""
    image_path = r"C:\Users\<USER>\OneDrive\Pictures\Screenshots\Screenshot 2025-07-31 111655.png"

    if not os.path.exists(image_path):
        logger.error(f"Test image not found: {image_path}")
        return None

    try:
        # Read the image file
        with open(image_path, 'rb') as f:
            image_bytes = f.read()

        # Convert to base64
        base64_data = base64.b64encode(image_bytes).decode('utf-8')

        return {
            'name': 'Screenshot 2025-07-31 111655.png',
            'base64': base64_data,
            'dataUrl': f'data:image/png;base64,{base64_data}'
        }
    except Exception as e:
        logger.error(f"Failed to load test image: {str(e)}")
        return None

def test_image_upload():
    """Test the image upload functionality."""
    logger.info("Starting image upload test...")
    
    # Load test image
    test_image = create_test_image()
    if not test_image:
        logger.error("Failed to load test image, aborting test")
        return
    logger.info(f"Loaded test image: {test_image['name']}")
    
    # Prepare request payload
    payload = {
        'message': 'Please modify this uploaded image to make it brighter and more vibrant.',
        'images': [test_image],
        'parameters': {}
    }
    
    # Send request to the API
    try:
        logger.info("Sending request to /api/chat...")
        response = requests.post(
            'http://localhost:5000/api/chat',
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        logger.info(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info("✅ Request successful!")
            logger.info(f"Response: {data.get('response', 'No response')}")
            logger.info(f"Uploaded images count: {data.get('uploaded_images_count', 0)}")
            logger.info(f"Processed images count: {data.get('processed_images_count', 0)}")
            
            if data.get('image_processing_errors'):
                logger.warning(f"Image processing errors: {data['image_processing_errors']}")
                
        else:
            logger.error(f"❌ Request failed with status {response.status_code}")
            logger.error(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Request exception: {str(e)}")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")

def test_health_endpoint():
    """Test the health endpoint to ensure the service is running."""
    try:
        response = requests.get('http://localhost:5000/api/health', timeout=5)
        if response.status_code == 200:
            logger.info("✅ Health check passed")
            return True
        else:
            logger.error(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Health check error: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("=== Image Upload Test ===")
    
    # First check if service is running
    if test_health_endpoint():
        # Run the image upload test
        test_image_upload()
    else:
        logger.error("Service is not running or not accessible")
