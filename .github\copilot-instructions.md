# AI Agent Instructions for Image Generation Agent

## Project Overview
This is a sophisticated AI image generation system built with LangChain, featuring multi-turn conversations, reference image support, and advanced AI tool orchestration. The system has both CLI and Flask web interfaces. Image generation/editing has been migrated to Black Forest Labs FLUX.1 Kontext (BFL) and the MCP server is now OpenAI-independent for image workflows. The web app may still use OpenAI for non-image LLM reasoning.

## Architecture & Key Components

### Core Agent Architecture (`src/agent.py`)
- Image tools migrated to BFL: `FluxKontextTool` is the primary image tool
- Web agent still uses OpenAI LLM for chat/reasoning (non-image paths)
- Custom Ruleset Support: Runtime agent behavior modification via system prompt injection
- Tool Priority Logic: Prefer BFL for any generation/edit where images or uploads are present

### Tool Hierarchy & Selection Patterns
```python
# Primary tool for images (BFL)
FluxKontextTool(prompt="instruction", image_b64=optional_base_image, aspect_ratio="1:1", bfl_model="pro")

# Non-image LLM
LLMIntegration(model="gpt-4o-mini")  # used for reasoning/chat in web app
```

### Multi-Turn Conversation Flow
Continuation and iterative edits are supported by passing a base image (as `image_b64` or `image_uri`) back into `FluxKontextTool` with new instructions. There is no Responses `previous_response_id` tracking on the BFL path; iterations are image-in/image-out.

## Development Patterns

### Testing Strategy
- **Mock-Heavy Architecture**: Use `@patch` for external dependencies (OpenAI LLM for text, and HTTP calls to BFL Kontext API via `requests`)
- **Tool Isolation Testing**: `FluxKontextTool` submit→poll→save is tested independently with mocked responses (both signed URLs and base64 payloads)
- **MCP Endpoint Testing**: Validate tool discovery and tool invocation through the MCP server
- **Web Endpoint Testing (optional)**: Flask test client for `/api/` flows when using provider=bfl
- **Custom Ruleset Validation**: Security checks for prompt injection prevention

Example test pattern:
```python
@patch('src.agent.LLMIntegration')
@patch('src.agent.ResponsesImageTool') 
def test_feature(self, mock_tool, mock_llm):
    # Mock setup, then test agent behavior
```

### Configuration Management (`config/settings.py`)
Environment-driven configuration with validation.

Key additions for BFL migration:
- `BFL_API_KEY` (required for image workflows)
- `PROVIDER_DEFAULT='bfl'` for images
- `BFL_DEFAULT_MODEL` (e.g., `kontext_pro`), `BFL_ASPECT_DEFAULT` (e.g., `1:1`)
- `SIZE_TO_ASPECT` mapping for legacy size hints

Validation no longer requires OpenAI image keys when running MCP image tools; OpenAI settings remain for the LLM.

### Image Processing Pipeline (`utils/image_encoder.py`)
- **Upload Processing**: Validates format/size, creates temporary files, base64 encoding
- **Reference Image Workflow**: File path validation, format conversion for API consumption
- **Cleanup Management**: Automatic temporary file cleanup after processing

## Critical Workflows

### CLI Interactive Mode (`main.py`)
Advanced command system with context-aware responses (unchanged). For images, the system now directs requests to BFL via `FluxKontextTool`.

### Web Interface Patterns (`web_app.py`)
- **Provider Hinting**: Defaults to provider=bfl; forwards `bfl_model`, `aspect_ratio`, `output_format`, etc.
- **Message Enhancement**: Automatic API mode instruction injection before agent invocation
- **Image Upload Processing**: Multi-file upload with error aggregation and cleanup
- **Session Management**: Custom ruleset storage per IP address (simple session tracking)
- **Parameter Handling**: JSON parameter passing with message augmentation

### Docker Deployment
Production-ready containerization with health checks, volume mounts for persistent image storage, and proper environment variable handling.

MCP-only server image: `Dockerfile.mcp` exposes the SSE MCP server (no web UI). Health endpoint: `/api/health`.

## Integration Points

### Integrations
- **BFL Kontext API**: Primary integration for image generation/editing (submit → poll → save flow; supports `pro` and `max` variants)
- **OpenAI LLM**: Used for non-image reasoning in the web app paths

### LangChain Tool Architecture
Image tool implements BaseTool with Pydantic input schema (`FluxKontextTool`). The agent binds this tool and executes via AgentExecutor with error handling and retry logic.

### File System Conventions
- `generated_images/` - Auto-created storage for all generated images with timestamp naming
- `config/` - Centralized configuration with environment variable loading
- `src/` - Core agent and tool implementations
- `utils/` - Shared utilities (image processing, encoding)
- `templates/` and `static/` - Web interface assets

## Debugging & Development

### Agent Behavior Analysis
Enable verbose logging in AgentExecutor to trace tool selection and execution. The agent's system prompt contains extensive tool selection logic that can be inspected by examining `_get_default_system_prompt()`.

### Tool Testing
Each tool has a `test_*` method for connectivity/functionality verification. Run comprehensive tests via `python main.py test` for full system validation.

### Web Interface Development
Flask app with CORS enabled, uses `render_template` for HTML serving and JSON API endpoints under `/api/`. Development mode available via direct `python web_app.py` execution.

## Project-Specific Conventions

- **Error Handling**: All tool methods return string responses, errors included in response text rather than exceptions
- **Image Storage**: Automatic timestamped filenames, no user-specified naming
- **Tool Selection**: User message enhancement pattern for API mode control rather than direct tool specification
- **Configuration Validation**: Settings validation required before any agent initialization
- **Session Persistence**: Simple IP-based session tracking for web interface, no database required

This codebase prioritizes user experience through intelligent tool orchestration while maintaining fallback compatibility and comprehensive testing coverage.

## MCP Tools Server (Option A) — Workflow and Tools

This repository includes a tools-only MCP server that exposes the BFL image generation/editing capabilities over SSE. Use this section when wiring IDEs or agents to call the tools directly via MCP.

### Transport and Endpoints
- Transport: SSE-only (streamable HTTP disabled)
- Endpoints:
    - GET / or GET /sse — establish an SSE session and run the MCP server
    - POST / or POST /messages — send JSON-RPC messages for tools/call and other MCP operations
    - GET /api/health — returns {"status":"ok","server":"mcp-sse"}

Notes:
- Both "/" and "/messages" accept JSON-RPC POSTs for broad client compatibility.
- Responses embed file:// resources; blobs may be inlined for small images.

### Resources and File Access
- Resource root: `file://<abs_path_to>/generated_images` is listed as “Generated Images Root”.
- Reading resources: Only file:// URIs within `generated_images/` are permitted; directory traversal is blocked.
- Files are saved to `generated_images/` with timestamped names. Typical patterns:
    - BFL final: `bfl_kontext_<variant>_YYYYMMDD_HHMMSS_<prompt_snippet>.<ext>`
    - Legacy (pre-migration): various `responses_api_*` or `generated_image_*` entries

### Tool Catalog (MCP list_tools)
The server advertises two BFL tools. Inputs are JSON objects; unspecified fields use sane defaults set in code.

1) image_generate_bfl_kontext
- What: Generate an image using BFL FLUX.1 Kontext (pro/max).
- Inputs (JSON):
    - prompt: string (required)
    - aspect_ratio?: string (e.g., "1:1", "3:4")
    - seed?: int
    - output_format?: 'jpeg' | 'png' (default: 'png')
    - safety_tolerance?: 0..6 (default: 2)
    - prompt_upsampling?: boolean
    - bfl_model?: 'pro' | 'max' (default from Settings)
- Output: Text summary and a resource attachment for the most recent file in `generated_images/` when present.

2) image_edit_bfl_kontext
- What: Edit an image using BFL FLUX.1 Kontext (pro/max). Provide a base image as `image_b64` or `image_uri`.
- Inputs (JSON):
    - prompt: string (required)
    - image_b64?: string (base64-encoded image)
    - image_uri?: string (local file path or file:// URI)
    - aspect_ratio?: string
    - seed?: int
    - output_format?: 'jpeg' | 'png'
    - safety_tolerance?: 0..6
    - prompt_upsampling?: boolean
    - bfl_model?: 'pro' | 'max'
- Output: Text summary and a resource attachment for the saved image.

### Defaults and Conventions
- Defaults used by the BFL path: `output_format='png'`, `safety_tolerance=2`, `aspect_ratio` falls back to Settings default.
- All tool methods return text (including errors) rather than raising exceptions; the MCP wrapper relays these results and attaches a file resource when available.

### Quick Verification
- Health check: GET /api/health
- Tool discovery: MCP list_tools shows the two BFL tools above.
- Resource listing: lists the `generated_images/` directory.

### Practical Guidance
- For edits, prefer `image_b64` to avoid host path visibility issues when containerized.
- Aspect ratios supported by Kontext are wide (approx. 3:7 to 7:3); unsupported inputs may be normalized by the tool.
- The tool saves from BFL signed URLs (e.g., `result.sample`) or base64 payloads automatically.
