# BFL MCP Tools Testing Notes

Date: 2025-08-26

This document summarizes the verification of the MCP tools after migrating image workflows to Black Forest Labs FLUX.1 Kontext.

## Endpoints and Client Config

- Health endpoint: GET /api/health — returns {"status":"ok","server":"mcp-sse"}
- JSON-RPC over HTTP (SSE transport adapter):
  - POST / and POST /messages supported
  - Added alias: POST /mcp (maps to /messages) for VS Code configs
- VS Code mcp.json can point to http://localhost:8001/mcp

## Tools Catalog

`tools/list` returns exactly these tools:
- image_generate_bfl_kontext
- image_edit_bfl_kontext

Legacy shims exist internally but are not advertised by `tools/list`.

## Happy Paths

- Generate: image_generate_bfl_kontext with prompt + aspect_ratio saves a file named
  `bfl_kontext_<variant>_YYYYMMDD_HHMMSS_<prompt_snippet>.(png|jpg)` in `generated_images/`.
  The MCP result includes a text summary and an EmbeddedResource pointing to `file://...` of the saved file.

- Edit: image_edit_bfl_kontext accepts:
  - image_uri as file:// URI
  - image_uri as plain OS path
  - image_b64 for container scenarios
  A new edited file is saved and attached as a resource.

## Parameters Tested

- bfl_model: pro, max — endpoint selection checks
- output_format: png, jpeg — file extension and MIME
- aspect_ratio: 1:1, 3:4, invalid values (normalized or error text depending on provider)
- safety_tolerance: 0 and 6 — accepted and forwarded
- prompt_upsampling: true/false — forwarded
- seed: repeated calls with same seed noted may still produce variation (provider-specific determinism)

## Error Cases

- Missing prompt — returns error text
- Edit without base image — returns error text (now enforced in MCP tool adapter)
- Invalid image_uri or Windows path — `_resolve_image_path` falls back by basename in `generated_images/`
- Missing BFL_API_KEY — clear error text from `FluxKontextTool`

## Notes

- The SSE-only server in `src/mcp/server.py` adds a `/mcp` POST alias for clients configured that way.
- Edits prefer `image_b64` when possible to avoid host-path issues in containers.
- EmbeddedResource may inline a `blob` for small files; otherwise clients can use `read_resource` with the URI.

## How to Run Smoke Test

Ensure server is running (port 8001 mapped to container 8000) and set BFL_API_KEY in env.

PowerShell:

```
# Start server (example):
# docker run --rm -p 8001:8000 -e BFL_API_KEY=$Env:BFL_API_KEY -v "$PWD\generated_images:/app/generated_images" image-agent-mcp

$Env:BFL_API_KEY = "YOUR_KEY"
python scripts/smoke_test_bfl_mcp.py
```

This prints file paths of generated/edited images and key parameter echoes.
