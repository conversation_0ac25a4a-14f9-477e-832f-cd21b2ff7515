"""
Production-grade image encoding utilities for OpenAI Responses API.
Companion to image_decoder.py for complete image processing pipeline.

This module provides robust image validation, encoding, and preprocessing
for use as reference images in AI image generation workflows.
"""
import base64
import os
import mimetypes
from typing import Optional, List, Dict, Tuple
from PIL import Image, ImageOps
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ImageEncodingError(Exception):
    """Custom exception for image encoding errors."""
    pass

class ImageEncoder:
    """
    Production-grade image encoder for AI image generation workflows.
    
    Features:
    - Robust image validation with multiple checks
    - Automatic format detection and MIME type handling
    - Size optimization and memory management
    - Comprehensive error handling and logging
    - Security-focused file validation
    """
    
    # Configuration constants
    SUPPORTED_FORMATS = {'.png', '.jpg', '.jpeg', '.webp', '.bmp', '.tiff'}
    MAX_SIZE_MB = 50
    MAX_DIMENSION = 4096  # Max width or height
    MIN_DIMENSION = 32    # Min width or height
    QUALITY_THRESHOLD_MB = 10  # Auto-optimize images larger than this
    
    # MIME type mapping
    MIME_TYPES = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.webp': 'image/webp',
        '.bmp': 'image/bmp',
        '.tiff': 'image/tiff'
    }
    
    @classmethod
    def encode_image_to_base64(cls, file_path: str, optimize: bool = True) -> Optional[str]:
        """
        Convert image file to base64 string with optional optimization.
        
        Args:
            file_path: Path to the image file
            optimize: Whether to optimize large images for better performance
            
        Returns:
            Base64 encoded string or None if encoding fails
            
        Raises:
            ImageEncodingError: If encoding fails due to validation or processing errors
        """
        try:
            # Validate the image first
            validation_result = cls.validate_image(file_path)
            if not validation_result['valid']:
                raise ImageEncodingError(f"Image validation failed: {validation_result['error']}")
            
            file_path = Path(file_path)
            original_size_mb = file_path.stat().st_size / (1024 * 1024)
            
            # Read and potentially optimize the image
            if optimize and original_size_mb > cls.QUALITY_THRESHOLD_MB:
                logger.info(f"Optimizing large image: {file_path.name} ({original_size_mb:.1f}MB)")
                image_data = cls._optimize_image(file_path)
            else:
                with open(file_path, "rb") as f:
                    image_data = f.read()
            
            # Encode to base64
            base64_data = base64.b64encode(image_data).decode("utf-8")
            
            # Log success with metrics
            final_size_mb = len(image_data) / (1024 * 1024)
            compression_ratio = (1 - final_size_mb / original_size_mb) * 100 if optimize else 0
            
            logger.info(
                f"Successfully encoded: {file_path.name} | "
                f"Original: {original_size_mb:.1f}MB | "
                f"Final: {final_size_mb:.1f}MB | "
                f"Base64 length: {len(base64_data):,} chars"
                + (f" | Compressed: {compression_ratio:.1f}%" if compression_ratio > 0 else "")
            )
            
            return base64_data
            
        except ImageEncodingError:
            raise
        except Exception as e:
            error_msg = f"Failed to encode image {file_path}: {str(e)}"
            logger.error(error_msg)
            raise ImageEncodingError(error_msg) from e
    
    @classmethod
    def validate_image(cls, file_path: str) -> Dict[str, any]:
        """
        Comprehensive image validation with detailed error reporting.
        
        Args:
            file_path: Path to the image file
            
        Returns:
            Dict with validation results: {'valid': bool, 'error': str, 'info': dict}
        """
        try:
            file_path = Path(file_path)
            
            # Check file existence
            if not file_path.exists():
                return {'valid': False, 'error': f"File not found: {file_path}"}
            
            # Check if it's a file (not directory)
            if not file_path.is_file():
                return {'valid': False, 'error': f"Path is not a file: {file_path}"}
            
            # Check file extension
            ext = file_path.suffix.lower()
            if ext not in cls.SUPPORTED_FORMATS:
                return {
                    'valid': False, 
                    'error': f"Unsupported format '{ext}'. Supported: {', '.join(cls.SUPPORTED_FORMATS)}"
                }
            
            # Check file size
            size_bytes = file_path.stat().st_size
            size_mb = size_bytes / (1024 * 1024)
            
            if size_mb > cls.MAX_SIZE_MB:
                return {
                    'valid': False,
                    'error': f"File too large: {size_mb:.1f}MB > {cls.MAX_SIZE_MB}MB limit"
                }
            
            if size_bytes == 0:
                return {'valid': False, 'error': "File is empty"}
            
            # Validate image content with PIL
            try:
                with Image.open(file_path) as img:
                    # Check image dimensions
                    width, height = img.size
                    
                    if width < cls.MIN_DIMENSION or height < cls.MIN_DIMENSION:
                        return {
                            'valid': False,
                            'error': f"Image too small: {width}x{height} < {cls.MIN_DIMENSION}px minimum"
                        }
                    
                    if width > cls.MAX_DIMENSION or height > cls.MAX_DIMENSION:
                        return {
                            'valid': False,
                            'error': f"Image too large: {width}x{height} > {cls.MAX_DIMENSION}px maximum"
                        }
                    
                    # Verify image integrity
                    img.verify()
                    
                    # Collect image info
                    info = {
                        'format': img.format,
                        'mode': img.mode,
                        'size': (width, height),
                        'file_size_mb': size_mb,
                        'mime_type': cls.MIME_TYPES.get(ext, 'application/octet-stream')
                    }
                    
                    return {'valid': True, 'error': None, 'info': info}
                    
            except Exception as e:
                return {'valid': False, 'error': f"Invalid image file: {str(e)}"}
                
        except Exception as e:
            return {'valid': False, 'error': f"Validation error: {str(e)}"}
    
    @classmethod
    def _optimize_image(cls, file_path: Path) -> bytes:
        """
        Optimize image for better performance while maintaining quality.
        
        Args:
            file_path: Path to the image file
            
        Returns:
            Optimized image data as bytes
        """
        with Image.open(file_path) as img:
            # Convert to RGB if necessary (for JPEG compatibility)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparency
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # Resize if too large
            max_size = (2048, 2048)  # Reasonable size for AI processing
            if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                logger.info(f"Resized image to {img.size}")
            
            # Optimize and save to bytes
            from io import BytesIO
            output = BytesIO()
            
            # Choose optimal format and quality
            if file_path.suffix.lower() in {'.jpg', '.jpeg'}:
                img.save(output, format='JPEG', quality=85, optimize=True)
            elif file_path.suffix.lower() == '.png':
                img.save(output, format='PNG', optimize=True)
            else:
                # Convert to JPEG for other formats
                img.save(output, format='JPEG', quality=85, optimize=True)
            
            return output.getvalue()
    
    @classmethod
    def prepare_reference_images(cls, file_paths: List[str], max_images: int = 4) -> List[Dict[str, str]]:
        """
        Prepare multiple images for OpenAI Responses API input.
        
        Args:
            file_paths: List of image file paths
            max_images: Maximum number of images to process
            
        Returns:
            List of formatted image dictionaries for API consumption
            
        Raises:
            ImageEncodingError: If any critical encoding errors occur
        """
        if not file_paths:
            return []
        
        # Limit number of images
        file_paths = file_paths[:max_images]
        reference_images = []
        errors = []
        
        logger.info(f"Preparing {len(file_paths)} reference images for Responses API")
        
        for i, file_path in enumerate(file_paths, 1):
            try:
                # Validate first
                validation = cls.validate_image(file_path)
                if not validation['valid']:
                    error_msg = f"Image {i} validation failed: {validation['error']}"
                    logger.warning(error_msg)
                    errors.append(error_msg)
                    continue
                
                # Encode to base64
                base64_data = cls.encode_image_to_base64(file_path)
                if not base64_data:
                    error_msg = f"Image {i} encoding failed: {file_path}"
                    logger.warning(error_msg)
                    errors.append(error_msg)
                    continue
                
                # Get MIME type
                ext = Path(file_path).suffix.lower()
                mime_type = cls.MIME_TYPES.get(ext, 'image/jpeg')
                
                # Format for API
                reference_images.append({
                    "type": "input_image",
                    "image_url": f"data:{mime_type};base64,{base64_data}"
                })
                
                logger.info(f"Successfully prepared reference image {i}/{len(file_paths)}")
                
            except Exception as e:
                error_msg = f"Error processing image {i} ({file_path}): {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        # Log summary
        success_count = len(reference_images)
        logger.info(f"Reference image preparation complete: {success_count}/{len(file_paths)} successful")
        
        if errors:
            logger.warning(f"Encountered {len(errors)} errors during preparation")
            for error in errors:
                logger.warning(f"  - {error}")
        
        if not reference_images and file_paths:
            raise ImageEncodingError("No valid reference images could be prepared")
        
        return reference_images
    
    @classmethod
    def get_image_info(cls, file_path: str) -> Optional[Dict[str, any]]:
        """
        Get detailed information about an image file.

        Args:
            file_path: Path to the image file

        Returns:
            Dictionary with image information or None if invalid
        """
        validation = cls.validate_image(file_path)
        return validation.get('info') if validation['valid'] else None

    @classmethod
    def process_uploaded_images(cls, uploaded_images: List[Dict[str, str]], temp_dir: str = "temp_uploads") -> Dict[str, any]:
        """
        Process images uploaded from web interface for LLM tool integration.

        Args:
            uploaded_images: List of dicts with 'name', 'base64', 'dataUrl' keys
            temp_dir: Directory to temporarily save uploaded images

        Returns:
            Dict with processed image data for LLM tools:
            {
                'reference_images': List[Dict] - formatted for ResponsesImageInput
                'base_images': List[str] - base64 data for ImageEditInput
                'temp_files': List[str] - paths to temporary files (for cleanup)
                'success_count': int,
                'errors': List[str]
            }
        """


        if not uploaded_images:
            return {
                'reference_images': [],
                'base_images': [],
                'temp_files': [],
                'success_count': 0,
                'errors': []
            }

        # Create temporary directory
        os.makedirs(temp_dir, exist_ok=True)

        reference_images = []
        base_images = []
        temp_files = []
        errors = []

        logger.info(f"Processing {len(uploaded_images)} uploaded images from web interface")

        for i, img_data in enumerate(uploaded_images, 1):
            try:
                # Extract data
                name = img_data.get('name', f'uploaded_image_{i}')
                base64_data = img_data.get('base64', '')

                if not base64_data:
                    errors.append(f"Image {i} ({name}): No base64 data provided")
                    continue

                # Decode base64 to bytes
                try:
                    image_bytes = base64.b64decode(base64_data)
                except Exception as e:
                    errors.append(f"Image {i} ({name}): Invalid base64 data - {str(e)}")
                    continue

                # Create temporary file
                temp_file = os.path.join(temp_dir, f"upload_{i}_{name}")

                # Save to temporary file for validation
                with open(temp_file, 'wb') as f:
                    f.write(image_bytes)

                temp_files.append(temp_file)

                # Validate the image
                validation = cls.validate_image(temp_file)
                if not validation['valid']:
                    errors.append(f"Image {i} ({name}): {validation['error']}")
                    continue

                # Re-encode with optimization for consistency
                optimized_base64 = cls.encode_image_to_base64(temp_file, optimize=True)
                if not optimized_base64:
                    errors.append(f"Image {i} ({name}): Failed to re-encode")
                    continue

                # Get MIME type
                ext = Path(name).suffix.lower()
                mime_type = cls.MIME_TYPES.get(ext, 'image/png')

                # Format for ResponsesImageInput (reference images)
                reference_images.append({
                    "type": "input_image",
                    "image_url": f"data:{mime_type};base64,{optimized_base64}"
                })

                # Store base64 for ImageEditInput (base images)
                base_images.append(optimized_base64)

                logger.info(f"Successfully processed uploaded image {i}/{len(uploaded_images)}: {name}")

            except Exception as e:
                error_msg = f"Error processing uploaded image {i}: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)

        success_count = len(reference_images)
        logger.info(f"Upload processing complete: {success_count}/{len(uploaded_images)} successful")

        if errors:
            logger.warning(f"Encountered {len(errors)} errors during upload processing")
            for error in errors:
                logger.warning(f"  - {error}")

        return {
            'reference_images': reference_images,
            'base_images': base_images,
            'temp_files': temp_files,
            'success_count': success_count,
            'errors': errors
        }


# Convenience functions for backward compatibility
def encode_image_to_base64(file_path: str) -> Optional[str]:
    """Convenience function for simple base64 encoding."""
    try:
        return ImageEncoder.encode_image_to_base64(file_path)
    except ImageEncodingError:
        return None

def validate_image(file_path: str) -> bool:
    """Convenience function for simple validation."""
    return ImageEncoder.validate_image(file_path)['valid']

