# Langchain Image Generation Agent

A sophisticated AI agent built with LangChain that combines conversational AI capabilities with advanced image generation. Image generation/editing has been migrated to Black Forest Labs FLUX.1 Kontext (BFL). The MCP tools server is OpenAI-independent for images, while the web app may continue to use OpenAI for non-image LLM reasoning.

## 🚀 Features

- **Conversational AI**: Powered by OpenAI's `gpt-4o-mini` for natural language understanding and reasoning (web app)
- **Advanced Image Generation (BFL)**: Integrated with Black Forest Labs FLUX.1 Kontext (`pro`/`max`) for image creation and edits
- **Iterative Image Editing**: Provide the previous image and new instructions to refine results
- **Reference Image Support**: Use your own images for style and content guidance
- **Streaming Generation**: (legacy path) Streaming via OpenAI Responses is deprecated here; BFL path performs submit → poll
- **Smart Tool Selection**: Automatically prefers the BFL image tool when image requests/uploads are detected
- **Interactive Interface**: Enhanced command-line interface with advanced features
- **Comprehensive Testing**: Built-in test suite for all components
- **Memory Management**: Maintains conversation context and generation history

## 🏗️ Project Structure

```
ImageAgent/
├── config/
│   ├── __init__.py
│   └── settings.py              # Configuration and environment variables
├── src/
│   ├── __init__.py
│   ├── agent.py                 # Main agent updated to use BFL image tool
│   ├── llm_integration.py       # Language model integration
│   ├── bfl_kontext_tool.py      # PRIMARY: BFL FLUX.1 Kontext submit→poll→save tool
│   ├── mcp/                     # MCP SSE tools server
│   ├── responses_image_tool.py  # Legacy OpenAI Responses tool (kept for reference)
│   ├── image_generation_tool.py # Legacy OpenAI gpt-image-1 generator (deprecated)
│   └── image_edit_tool.py       # Legacy OpenAI gpt-image-1 editor (deprecated)
├── utils/
│   ├── __init__.py
│   └── image_encoder.py         # Image validation and encoding utilities
├── generated_images/            # Auto-created directory for saved images
├── notebooks/                   # Jupyter notebooks for experimentation
├── LLM_docs/                   # API docs (OpenAI and migration notes)
├── main.py                     # Enhanced main application entry point (CLI)
├── requirements.txt            # Python dependencies
├── docker-compose.yml          # Docker deployment configuration
├── Dockerfile                  # Container configuration (web app)
├── Dockerfile.mcp              # Container configuration (MCP tools-only server)
├── .env                       # Environment variables (API keys)
├── .gitignore                 # Git ignore file
├── plan.md                    # Development plan
├── WEB_DEPLOYMENT_GUIDE.md    # Web deployment instructions
└── README.md                  # This file
```

## 🛠️ Installation

### Prerequisites

- Python 3.11 recommended
- BFL API key for image tools (BFL_FLUX_KONTEXT)
- OpenAI API key (only if using the web app’s non-image LLM)

### Setup

1. **Clone or navigate to the project directory:**
   ```bash
   cd Image_Agent
   ```

2. **Create and activate a virtual environment:**
   ```bash
   python -m venv venv
   
   # On Windows:
   venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   - `.env` (or environment) should include:
   ```
   # Required for BFL image tools
   BFL_API_KEY=your_bfl_api_key_here
   # Optional (web app LLM)
   OPENAI_API_KEY=your_openai_api_key_here
   ```

## 🎮 Usage

### Enhanced Interactive Mode (CLI)

Run the agent in interactive mode for the full conversational experience:

```bash
python main.py
```

### 🚀 Commands

- **`refs [path1] [path2]...`** - Load reference images for style/content guidance
- **`continue [instruction]`** - Edit the last generated image with new instructions
- Note: streaming via OpenAI Responses has been deprecated in favor of BFL’s submit→poll flow.
- **`clear-refs`** - Remove all loaded reference images
- **`history`** - Show generation history with response IDs

### 🔧 Standard Commands

- **`clear`** - Clear chat history
- **`test`** - Run system tests
- **`exit/quit`** - End the session

### 💡 Usage Examples

#### Multi-turn Image Editing
```
🧑 You: Generate a sunset landscape
🤖 Agent: [Creates beautiful sunset landscape]

🧑 You: continue make it more dramatic with storm clouds
🤖 Agent: [Enhances the image with dramatic storm clouds]

🧑 You: now add a lighthouse in the distance
🤖 Agent: [Adds lighthouse while maintaining the dramatic scene]
```

#### Reference Image Workflow
```
🧑 You: refs style_reference.jpg logo.png
🤖 Agent: ✅ Successfully loaded 2 reference image(s)

🧑 You: Create a modern website banner using these references
🤖 Agent: [Generates banner incorporating style and logo elements]
```

#### Streaming Generation
```
🧑 You: stream Create a detailed fantasy castle on a floating island
🤖 Agent: 🎬 Starting streaming generation...
         [Shows partial images as they develop]
         ✅ Final image completed!
```

### Single Query Mode (CLI)

Run a single query from the command line:

```bash
python main.py "Generate an image of a futuristic city at sunset"
```

### Testing (CLI)

Run the comprehensive test suite:

```bash
python main.py test
```

## 🏛️ Architecture (Post-Migration)

### 🔧 Core Components

1. **FluxKontextTool** (`src/bfl_kontext_tool.py`) - **PRIMARY IMAGE TOOL**
   - BFL FLUX.1 Kontext integration (pro/max)
   - Submit → Poll → Save flow, including signed URL (result.sample) support
   - Accepts base64 or file path for edits, plus `aspect_ratio`, `output_format`, `seed`, `prompt_upsampling`, `safety_tolerance`
   - Robust image saving to `generated_images/`

2. **Agent** (`src/agent.py`)
   - Updated system prompt to reflect BFL-only image guidance
   - Detects uploads and `provider=bfl` hints; directly invokes `FluxKontextTool`
   - Uses OpenAI LLM only for non-image reasoning paths

3. **Image Encoder Utility** (`utils/image_encoder.py`)
   - Image validation and format checking
   - Base64 encoding for API consumption
   - Reference image preparation and optimization
   - File size and format validation

4. **LLM Integration** (`src/llm_integration.py`)
   - OpenAI language model connectivity (web app only)
   - Model configuration and testing
   - Error handling and validation

5. **Legacy Tools** (Deprecated)
   - `ResponsesImageTool`, `OpenAIImageGenerator`, `OpenAIImageEditor` retained for reference

6. **Configuration** (`config/settings.py`)
   - Centralized configuration management
   - Environment variable handling
   - Responses API settings and validation

### 🎯 Tool Selection Logic

The agent automatically prefers the BFL image tool whenever the task involves generating or editing images (especially if uploads are present or provider=bfl hints are supplied). The LLM is used for non-image chat/reasoning.

### 🔄 Multi-turn Image Flow with BFL

1. **Initial Generation**: User requests image → `FluxKontextTool` generates image and saves it
2. **Continuation/Edit**: User supplies the previous image (`image_b64` or `image_uri`) with new instructions → tool edits and saves
3. **Iteration**: Repeat with the latest image as the new base; no `previous_response_id` is used on BFL path

## 🎨 Advanced Capabilities

### Multi-turn Image Editing
- **Automatic Detection**: Agent recognizes continuation phrases
- **Context Preservation**: Maintains visual consistency across edits
- **Iterative Refinement**: Build complex images through conversation

### Reference Image Support
- **Style Transfer**: "Make it look like this style"
- **Composition Guidance**: "Based on this layout"
- **Content Integration**: "Include elements from these images"
- **Format Validation**: Automatic checking of image formats and sizes

### Streaming Generation
- Not supported on the BFL path. The workflow is submit → poll for completion.

### Intelligent Prompting
- The agent still supports prompt enhancement and context integration. Revised prompt visibility is specific to legacy OpenAI paths.

## 🐳 Docker Deployment

Two targets:

1) Web App (Flask UI)
```
docker-compose up --build
# Access at http://localhost:5000
```

2) MCP Tools-Only Server
```
# Build the MCP image
docker build -f Dockerfile.mcp -t image-agent-mcp .
# Run (map host 8001 to container 8000)
docker run --rm -p 8001:8000 -e BFL_API_KEY=YOUR_KEY -v %CD%\generated_images:/app/generated_images image-agent-mcp
# Health: http://localhost:8001/api/health
```

## ⚙️ Configuration

The agent can be configured through `config/settings.py`:

- **OpenAI LLM**: Default `gpt-4o-mini` (web app only)
- **Image Provider**: BFL FLUX.1 Kontext (default `kontext_pro`)
- **Image Parameters**: `aspect_ratio`, `output_format`, `seed`, `prompt_upsampling`, `safety_tolerance`
- **Provider Default**: `PROVIDER_DEFAULT='bfl'` for image workflows

## 🔍 Models and Providers

- **Language Model**: `gpt-4o-mini` (OpenAI) for non-image reasoning (web app)
- **Image Generation/Editing**: BFL FLUX.1 Kontext (`pro`/`max`)
- **Legacy Support**: OpenAI image tools retained as reference but deprecated

## 🚨 Troubleshooting

### Common Issues

1. **BFL API Key Error**
   ```
   Error: BFL_API_KEY environment variable is required
   ```
   - Solution: Ensure your environment contains a valid BFL API key with FLUX.1 Kontext access

2. **Reference/Base Image Errors**
   ```
   ❌ Reference image error: Invalid format/size
   ```
   - Solution: Use PNG, JPG, JPEG, or WEBP files under 50MB

3. **Edit Iteration Issues**
   ```
   ❌ Edit requires a base image
   ```
   - Solution: Supply `image_b64` or a valid `image_uri` to edit

4. **Streaming Not Available**
   - BFL path is submit → poll; streaming previews are not available.

### Debugging

Enable verbose logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

View generation history:
```bash
# In interactive mode
history
```

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Add comprehensive tests for new features
3. Update documentation for any changes
4. Ensure all tests pass before submitting changes
5. Test BFL tools (submit→poll), MCP endpoints, and web app integration (if used)

## 📄 License

This project is for educational and development purposes. Please ensure compliance with OpenAI's usage policies when using their APIs.

## 🙏 Acknowledgments

- Built with [LangChain](https://python.langchain.com/) framework
- Powered by [OpenAI](https://openai.com/) Responses API and models
- Follows LangChain best practices and patterns
- Implements cutting-edge multi-turn image generation workflows

---

## 🎯 Quick Start Examples

### Basic Generation (CLI)
```bash
python main.py
🧑 You: Create a serene mountain lake at dawn
```

### Multi-turn Editing (CLI)
```bash
🧑 You: Generate a cozy coffee shop interior
🤖 Agent: [Creates coffee shop image]
🧑 You: continue add more plants and warm lighting
🤖 Agent: [Enhances with plants and lighting]
```

### Reference-guided Creation (CLI)
```bash
🧑 You: refs architectural_style.jpg color_palette.png
🧑 You: Design a modern house using these references
```

### MCP Tools Server
Configure your IDE MCP client to point to the server, for example:

VS Code Insiders `mcp.json`:
```
{
   "servers": {
      "Image-gen_FLUX": {
         "url": "http://localhost:8001/mcp",
         "type": "http"
      }
   },
   "inputs": []
}
```

Tool names:
- `image_generate_bfl_kontext`
- `image_edit_bfl_kontext`

Ready to create amazing images with AI? Start with `python main.py` and explore the possibilities! 🚀
