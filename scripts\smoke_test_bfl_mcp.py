#!/usr/bin/env python3
"""
Smoke test for MCP BFL Kontext tools via JSON-RPC over HTTP.
- Verifies health endpoint
- tools/list returns only image_generate_bfl_kontext and image_edit_bfl_kontext
- Generates an image, confirms file saved and resource attached
- Edits the image using image_uri (file:// and plain path) and image_b64 variants
Prints saved file paths and key parameters used.

Usage (PowerShell):
  $env:BFL_API_KEY = "YOUR_KEY"; python scripts/smoke_test_bfl_mcp.py

Server defaults to http://localhost:8001, path /mcp (alias to /messages).
"""
import os
import sys
import json
import time
import base64
from urllib.parse import urljoin

try:
    import requests
except Exception:
    print("This script requires the 'requests' package. Install it in your venv.")
    sys.exit(1)

BASE_URL = os.environ.get("MCP_BASE_URL", "http://localhost:8001")
MCP_POST = urljoin(BASE_URL.rstrip('/') + '/', 'mcp')  # alias supported by server
HEALTH = urljoin(BASE_URL.rstrip('/') + '/', 'api/health')
GENERATED_DIR = os.environ.get("GENERATED_IMAGES_DIR", "generated_images")


def rpc(method: str, params=None, id=1):
    payload = {
        "jsonrpc": "2.0",
        "method": method,
        "id": id,
    }
    if params is not None:
        payload["params"] = params
    r = requests.post(MCP_POST, json=payload, timeout=30)
    r.raise_for_status()
    data = r.json()
    if 'error' in data:
        raise RuntimeError(f"RPC error: {data['error']}")
    return data


def get_texts_and_resources(result):
    texts = []
    resources = []
    for item in result.get('result', {}).get('content', []):
        if item.get('type') == 'text':
            texts.append(item.get('text', ''))
        elif item.get('type') == 'resource':
            res = item.get('resource', {})
            resources.append(res)
    return texts, resources


def ensure_health():
    r = requests.get(HEALTH, timeout=10)
    r.raise_for_status()
    j = r.json()
    assert j.get('status') == 'ok', f"Bad health: {j}"
    print("Health: OK")


def list_tools_expect():
    data = rpc("tools/list", id=2)
    tools = [t.get('name') for t in data.get('result', {}).get('tools', [])]
    print("Tools:", tools)
    assert set(tools) == {"image_generate_bfl_kontext", "image_edit_bfl_kontext"}, "Unexpected tools set"


def call_generate(prompt: str, aspect_ratio: str, output_format: str = 'png', bfl_model: str = 'pro'):
    params = {
        "name": "image_generate_bfl_kontext",
        "arguments": {
            "prompt": prompt,
            "aspect_ratio": aspect_ratio,
            "output_format": output_format,
            "bfl_model": bfl_model,
        }
    }
    data = rpc("tools/call", params=params, id=3)
    texts, resources = get_texts_and_resources(data)
    print("Generate text:\n", "\n".join(texts))
    assert any("Saved to:" in t or "Saved" in t for t in texts), "No 'Saved' text in response"
    assert resources, "No resource attached"
    uri = resources[0].get('uri')
    assert uri and uri.startswith('file://'), f"Bad resource uri: {uri}"
    # Also check local file exists
    path = uri[len('file://'):]
    if os.name == 'nt' and path.startswith('/'):
        path = path.lstrip('/')
    assert os.path.isfile(path), f"Generated file missing: {path}"
    print("Generated file:", path)
    return path, resources[0]


def call_edit(prompt: str, image_uri: str = None, use_file_scheme: bool = True, image_b64: str = None, aspect_ratio: str = None, output_format: str = 'png', bfl_model: str = 'pro'):
    args = {"prompt": prompt, "output_format": output_format, "bfl_model": bfl_model}
    if aspect_ratio:
        args["aspect_ratio"] = aspect_ratio
    if image_b64:
        args["image_b64"] = image_b64
    if image_uri:
        args["image_uri"] = ("file://" + image_uri) if use_file_scheme and not image_uri.startswith('file://') else image_uri
    params = {"name": "image_edit_bfl_kontext", "arguments": args}
    data = rpc("tools/call", params=params, id=4)
    texts, resources = get_texts_and_resources(data)
    print("Edit text:\n", "\n".join(texts))
    assert resources, "No resource attached on edit"
    uri = resources[0].get('uri')
    assert uri and uri.startswith('file://'), f"Bad resource uri: {uri}"
    path = uri[len('file://'):]
    if os.name == 'nt' and path.startswith('/'):
        path = path.lstrip('/')
    assert os.path.isfile(path), f"Edited file missing: {path}"
    print("Edited file:", path)
    return path, resources[0]


def to_b64(path: str) -> str:
    with open(path, 'rb') as f:
        return base64.b64encode(f.read()).decode('utf-8')


def main():
    if not os.environ.get('BFL_API_KEY'):
        print("Warning: BFL_API_KEY not set; live calls will fail. Set it to run the smoke test against BFL.")
    ensure_health()
    list_tools_expect()

    # 1) Generate (pro, png)
    gen_path, gen_res = call_generate("a scenic mountain at sunrise", aspect_ratio="3:2", output_format='png', bfl_model='pro')
    # 1b) Generate jpeg (max)
    call_generate("a scenic mountain at sunrise, wide angle", aspect_ratio="3:2", output_format='jpeg', bfl_model='max')

    # 2) Edit via file://
    call_edit("increase contrast and add light fog", image_uri=gen_path, use_file_scheme=True, aspect_ratio="3:2", output_format='png', bfl_model='pro')

    # 3) Edit via plain path
    call_edit("make colors warmer", image_uri=gen_path, use_file_scheme=False, output_format='jpeg', bfl_model='max')

    # 4) Edit via image_b64
    b64 = to_b64(gen_path)
    call_edit("add a small boat on the lake", image_b64=b64, aspect_ratio="1:1", output_format='png', bfl_model='pro')

    # 5) Parameter edges
    # aspect_ratio invalid -> expect graceful behavior (tool may normalize or error text)
    try:
        call_generate("test invalid aspect", aspect_ratio="9:0", output_format='png', bfl_model='pro')
    except AssertionError as e:
        print("Invalid aspect handled (no resource):", e)

    # safety_tolerance boundaries and prompt_upsampling true/false
    for tol in (0, 6):
        params = {
            "name": "image_generate_bfl_kontext",
            "arguments": {
                "prompt": f"boundary tol {tol}",
                "aspect_ratio": "1:1",
                "safety_tolerance": tol,
                "prompt_upsampling": (tol % 2 == 0),
            }
        }
        data = rpc("tools/call", params=params, id=100 + tol)
        texts, resources = get_texts_and_resources(data)
        print(f"Tolerance {tol} text:\n", "\n".join(texts))
        if resources:
            uri = resources[0].get('uri')
            print("Resource:", uri)

    # Seed repeat (provider may not be fully deterministic)
    seed_val = 12345
    pmt = "foggy forest trail"
    params_seed = {
        "name": "image_generate_bfl_kontext",
        "arguments": {
            "prompt": pmt,
            "aspect_ratio": "1:1",
            "seed": seed_val,
        }
    }
    d1 = rpc("tools/call", params=params_seed, id=2001)
    _, res1 = get_texts_and_resources(d1)
    d2 = rpc("tools/call", params=params_seed, id=2002)
    _, res2 = get_texts_and_resources(d2)
    print("Seed test resources:", [r.get('uri') for r in (res1[:1] + res2[:1]) if r])
    print("Note: Provider may still vary output despite same seed; record observations.")

    print("\nSmoke test completed.")


if __name__ == "__main__":
    main()
