from typing import Any, Dict, List, Optional

# Placeholder types to avoid import errors during scaffold. Replace with your MCP SDK types as needed.
try:
    from mcp.server import types as mcp_types  # type: ignore
except Exception:  # pragma: no cover
    class mcp_types:  # minimal shims aligned to MCP content schema
        class TextContent(dict):
            def __init__(self, text: str):
                super().__init__()
                self.update({"type": "text", "text": text})

        class EmbeddedResource(dict):
            def __init__(self, uri: str, mimeType: Optional[str] = None, text: Optional[str] = None, blob: Optional[str] = None):
                super().__init__()
                resource: Dict[str, Any] = {"uri": uri}
                if mimeType:
                    resource["mimeType"] = mimeType
                if text is not None:
                    resource["text"] = text
                if blob is not None:
                    resource["blob"] = blob
                # Wrap in MCP 'resource' content shape
                self.update({
                    "type": "resource",
                    "resource": resource,
                })

import os
from datetime import datetime

from src.bfl_kontext_tool import FluxKontextTool
from utils.image_encoder import ImageEncoder
from config.settings import Settings
from io import BytesIO
import base64
import ntpath

try:
    from PIL import Image, ImageDraw  # type: ignore
except Exception:  # pragma: no cover
    Image = None  # type: ignore
    ImageDraw = None  # type: ignore


_bfl = FluxKontextTool()


def _latest_file_in_generated() -> Optional[str]:
    root = Settings.GENERATED_IMAGES_DIR if hasattr(Settings, 'GENERATED_IMAGES_DIR') else 'generated_images'
    if not os.path.isdir(root):
        return None
    files = [os.path.join(root, f) for f in os.listdir(root) if f.lower().endswith((".png", ".jpg", ".jpeg", ".webp"))]
    if not files:
        return None
    files.sort(key=lambda p: os.path.getmtime(p), reverse=True)
    return os.path.abspath(files[0])


def list_tools() -> List[Dict[str, Any]]:
    """Return MCP tool metadata for discovery. Expose separate generate and edit tools."""
    return [
        {
            "name": "image_generate_bfl_kontext",
            "description": (
                "Generate an image using BFL FLUX.1 Kontext (pro/max).\n"
                "Inputs (JSON): {prompt: string, aspect_ratio?: string, seed?: int, output_format?: 'jpeg'|'png',\n"
                " safety_tolerance?: 0..6, prompt_upsampling?: boolean, bfl_model?: 'pro'|'max'}."
            ),
            "inputSchema": {
                "type": "object",
                "properties": {
                    "prompt": {"type": "string"},
                    "aspect_ratio": {"type": "string"},
                    "seed": {"type": "integer"},
                    "output_format": {"type": "string"},
                    "safety_tolerance": {"type": "integer"},
                    "prompt_upsampling": {"type": "boolean"},
                    "bfl_model": {"type": "string"}
                },
                "required": ["prompt"]
            }
        },
        {
            "name": "image_edit_bfl_kontext",
            "description": (
                "Edit an image using BFL FLUX.1 Kontext (pro/max). Provide a base image as image_b64 or image_uri.\n"
                "Inputs (JSON): {prompt: string, image_b64?: string, image_uri?: string, aspect_ratio?: string,\n"
                " seed?: int, output_format?: 'jpeg'|'png', safety_tolerance?: 0..6, prompt_upsampling?: boolean,\n"
                " bfl_model?: 'pro'|'max'}."
            ),
            "inputSchema": {
                "type": "object",
                "properties": {
                    "prompt": {"type": "string"},
                    "image_b64": {"type": "string"},
                    "image_uri": {"type": "string"},
                    "aspect_ratio": {"type": "string"},
                    "seed": {"type": "integer"},
                    "output_format": {"type": "string"},
                    "safety_tolerance": {"type": "integer"},
                    "prompt_upsampling": {"type": "boolean"},
                    "bfl_model": {"type": "string"}
                },
                "required": ["prompt"]
            }
        }
    ]


def call_image_edit_bfl_kontext(arguments: Dict[str, Any]) -> List[Dict[str, Any]]:
    prompt = str(arguments.get("prompt", "")).strip()
    if not prompt:
        return [mcp_types.TextContent("Error: 'prompt' is required.")]

    # Robust base image resolution: support image_b64, file://, plain paths, Windows backslashes, quoted paths
    img_b64 = arguments.get("image_b64")
    img_uri_raw = arguments.get("image_uri")
    resolved_uri = None
    if img_uri_raw:
        resolved_uri = _resolve_image_path(str(img_uri_raw))
        # If resolution fails but a file:// was provided with leading slash on Windows, try stripping it
        if not resolved_uri and isinstance(img_uri_raw, str) and img_uri_raw.startswith("file:///"):
            resolved_uri = _resolve_image_path("file://" + img_uri_raw.replace("file:///", "file://", 1))

    if not img_b64 and not resolved_uri:
        return [mcp_types.TextContent("Error: 'image_b64' or 'image_uri' is required for edits.")]

    result = _bfl._run(
        prompt=prompt,
        image_b64=img_b64,
        image_uri=resolved_uri,
        aspect_ratio=arguments.get("aspect_ratio"),
        seed=arguments.get("seed"),
        output_format=arguments.get("output_format") or "png",
        safety_tolerance=arguments.get("safety_tolerance", 2),
        prompt_upsampling=bool(arguments.get("prompt_upsampling")),
        model_variant=(arguments.get("bfl_model") or Settings.BFL_DEFAULT_MODEL.replace("kontext_", "")),
    )

    file_path = _latest_file_in_generated()
    out: List[Dict[str, Any]] = [mcp_types.TextContent(str(result))]
    if file_path:
        out.append(mcp_types.EmbeddedResource(uri=f"file://{file_path}", mimeType=_guess_mime(file_path)))
    return out


def call_image_generate_bfl_kontext(arguments: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Generate with BFL Kontext (no base image)."""
    prompt = str(arguments.get("prompt", "")).strip()
    if not prompt:
        return [mcp_types.TextContent("Error: 'prompt' is required.")]

    result = _bfl._run(
        prompt=prompt,
        image_b64=None,
        image_uri=None,
        aspect_ratio=arguments.get("aspect_ratio"),
        seed=arguments.get("seed"),
        output_format=arguments.get("output_format") or "png",
        safety_tolerance=arguments.get("safety_tolerance", 2),
        prompt_upsampling=bool(arguments.get("prompt_upsampling")),
        model_variant=(arguments.get("bfl_model") or Settings.BFL_DEFAULT_MODEL.replace("kontext_", "")),
    )

    file_path = _latest_file_in_generated()
    out: List[Dict[str, Any]] = [mcp_types.TextContent(str(result))]
    if file_path:
        out.append(mcp_types.EmbeddedResource(uri=f"file://{file_path}", mimeType=_guess_mime(file_path)))
    return out


# Remove legacy OpenAI and Responses handlers from the MCP surface in this migration


def call_image_generate_responses(arguments: Dict[str, Any]) -> List[Dict[str, Any]]:  # deprecated shim
    return [mcp_types.TextContent("This server now uses BFL FLUX.1 Kontext only. Use 'image_edit_bfl_kontext'.")]


def call_image_edit_responses(arguments: Dict[str, Any]) -> List[Dict[str, Any]]:  # deprecated shim
    return [mcp_types.TextContent("This server now uses BFL FLUX.1 Kontext only. Use 'image_edit_bfl_kontext'.")]


def _resolve_image_path(image_uri: str) -> Optional[str]:
    """Resolve an image path robustly for server-side access.

    - Accepts direct paths and file:// URIs.
    - Falls back to searching generated_images by basename.
    """
    if not image_uri:
        return None
    # file:// URIs
    if image_uri.startswith("file://"):
        p = image_uri[len("file://"):]
        if os.name == 'nt' and p.startswith('/'):
            p = p.lstrip('/')
        if os.path.isfile(p):
            return os.path.abspath(p)
    # direct path
    if os.path.isfile(image_uri):
        return os.path.abspath(image_uri)
    # basename within generated_images (handle Windows paths too)
    base = os.path.basename(image_uri)
    # Fallback to Windows-style basename if running on Linux container
    if (not base) or ('\\' in image_uri and ('\\' in base or base == image_uri)):
        base = ntpath.basename(image_uri)
    # As a last resort, strip any surrounding quotes and normalize separators
    base = base.strip().strip('"').strip("'")
    root = Settings.GENERATED_IMAGES_DIR if hasattr(Settings, 'GENERATED_IMAGES_DIR') else 'generated_images'
    candidate = os.path.join(root, base)
    if os.path.isfile(candidate):
        return os.path.abspath(candidate)
    return None


def _guess_mime(path: str) -> str:
    p = path.lower()
    if p.endswith('.png'):
        return 'image/png'
    if p.endswith('.jpg') or p.endswith('.jpeg'):
        return 'image/jpeg'
    if p.endswith('.webp'):
        return 'image/webp'
    return 'application/octet-stream'


def call_image_edit_mask_responses(arguments: Dict[str, Any]) -> List[Dict[str, Any]]:  # deprecated shim
    return [mcp_types.TextContent("Mask-based edits are not supported via Kontext. Use 'image_edit_bfl_kontext' without masks.")]


def _generate_mask_from_region(image_b64: str, region: Dict[str, Any]) -> str:
    """Create white (edit) / black (preserve) mask from bbox or polygon region definition."""
    # Decode to get image size
    img_bytes = base64.b64decode(image_b64)
    with Image.open(BytesIO(img_bytes)) as im:
        w, h = im.size

    mask = Image.new('L', (w, h), color=0)
    draw = ImageDraw.Draw(mask)

    norm = bool(region.get('normalized'))
    invert = bool(region.get('invert'))

    if 'bbox' in region and isinstance(region['bbox'], list) and len(region['bbox']) >= 4:
        x, y, rw, rh = region['bbox'][:4]
        if norm:
            x, y, rw, rh = x * w, y * h, rw * w, rh * h
        draw.rectangle([x, y, x + rw, y + rh], fill=255)
    elif 'polygon' in region and isinstance(region['polygon'], list) and region['polygon']:
        pts = region['polygon']
        if norm:
            pts = [(px * w, py * h) for px, py in pts]
        draw.polygon(pts, fill=255)
    else:
        raise ValueError("Region must include 'bbox' or 'polygon'.")

    if invert:
        mask = Image.eval(mask, lambda v: 255 - v)

    buf = BytesIO()
    mask.save(buf, format='PNG')
    return base64.b64encode(buf.getvalue()).decode('utf-8')
