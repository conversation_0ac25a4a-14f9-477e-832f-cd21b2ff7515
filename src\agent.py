"""
Langchain Image Generation Agent.
"""
from typing import List, Dict, Any
from langchain_core.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>plate, MessagesPlaceholder
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.agents import create_openai_tools_agent, AgentExecutor
from langchain_core.tools import BaseTool
from src.llm_integration import LLMIntegration
from src.bfl_kontext_tool import FluxKontextTool
from config.settings import Settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageGenerationAgent:
    """Main agent class that combines LLM reasoning with image generation capabilities."""
    
    def __init__(self):
        """Initialize the image generation agent."""
        logger.info("Initializing Image Generation Agent...")

        # Initialize custom ruleset support
        self.custom_ruleset = None
        self.default_system_prompt = self._get_default_system_prompt()

        # Initialize components
        self.llm_integration = LLMIntegration()

        # Image operations now handled via BFL FluxKontextTool only
        self.bfl_tool = FluxKontextTool()

        # Create the agent prompt
        self.prompt = self._create_agent_prompt()

        # Create tools list; primary image tool is BFL Kontext
        self.tools = [self.bfl_tool]

        # Bind tools to the LLM
        self.llm_with_tools = self.llm_integration.llm.bind_tools(self.tools)

        # Create the agent
        self.agent = create_openai_tools_agent(
            llm=self.llm_integration.llm,
            tools=self.tools,
            prompt=self.prompt
        )

        # Create agent executor
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3
        )

        logger.info("Image Generation Agent initialized successfully")
    
    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt."""
        return (
            "You are an advanced AI assistant specialized in image generation and editing using "
            "Black Forest Labs' FLUX.1 Kontext models (pro/max).\n\n"
            "TOOLS:\n"
            "- FluxKontextTool (BFL) — provide a prompt and optionally a base image (image_b64 or image_uri).\n"
            "  Masks and streaming are not supported; each request is stateless.\n\n"
            "ROUTING:\n"
            "- Default to FluxKontextTool for all image tasks.\n"
            "- If users supply a base image, call FluxKontextTool with that image as input_image.\n\n"
            "UPLOADED IMAGES:\n"
            "- When users upload images and ask to edit them, use the uploaded image as base.\n"
            "- If multiple are provided, prefer the first unless the user specifies otherwise.\n\n"
            "PARAMETERS (when specified by the user):\n"
            "- aspect_ratio: e.g., '1:1', '3:2', '2:3'.\n"
            "- model_variant: 'pro' or 'max'.\n"
            "- output_format: 'png' or 'jpeg'.\n\n"
            "GUIDANCE:\n"
            "- For iterative edits, users can provide the previous result as the base image.\n"
            "- Offer helpful suggestions, keep responses concise, and call the tool when appropriate."
        )

    def _get_current_system_prompt(self) -> str:
        """Get the current system prompt, including custom ruleset if set."""
        base_prompt = self.default_system_prompt

        if self.custom_ruleset:
            return f"""{base_prompt}

Additionally, you must follow these custom rules:
{self.custom_ruleset}

These custom rules take precedence over default behaviors when there's a conflict."""

        return base_prompt

    def _create_agent_prompt(self) -> ChatPromptTemplate:
        """
        Create enhanced system prompt with Responses API capabilities.

        Returns:
            ChatPromptTemplate: The prompt template for the agent with new tool priorities
        """
        system_message = self._get_current_system_prompt()

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_message),
            MessagesPlaceholder("chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder("agent_scratchpad")
        ])

        return prompt

    def set_custom_ruleset(self, ruleset: str):
        """Set a custom ruleset for the agent."""
        self.custom_ruleset = ruleset
        # Re-initialize the agent chain with new system prompt
        self._reinitialize_with_prompt(self._get_current_system_prompt())

    def clear_custom_ruleset(self):
        """Clear the custom ruleset and revert to default behavior."""
        self.custom_ruleset = None
        self._reinitialize_with_prompt(self.default_system_prompt)

    def ensure_custom_ruleset(self, ruleset: str):
        """Ensure the custom ruleset is applied if different from current."""
        if self.custom_ruleset != ruleset:
            self.set_custom_ruleset(ruleset)

    def _reinitialize_with_prompt(self, system_prompt: str):
        """Reinitialize the agent with a new system prompt."""
        # Update the prompt in the existing agent setup
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder("chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder("agent_scratchpad")
        ])

        # Recreate the agent with the new prompt
        self.agent = create_openai_tools_agent(
            llm=self.llm_integration.llm,
            tools=self.tools,
            prompt=self.prompt
        )

        # Update the executor
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3
        )

    def invoke(self, user_input: str, chat_history: List[BaseMessage] = None, processed_images: dict = None) -> str:
        """
        Process user input and generate a response.

        Args:
            user_input: The user's message/request
            chat_history: Optional list of previous messages
            processed_images: Optional dict with processed image data from ImageEncoder

        Returns:
            str: The agent's response
        """
        try:
            logger.info(f"Processing user input: '{user_input[:50]}...'")

            # Enhance input with image information if available
            enhanced_input = user_input
            if processed_images and processed_images.get('success_count', 0) > 0:
                # Add context about available images for the LLM (BFL path)
                image_count = processed_images['success_count']
                enhanced_input += (
                    f"\n\nNote: {image_count} uploaded image(s) available. Use FluxKontextTool with the prompt and the uploaded image as base."
                )
                logger.info(f"Enhanced input with {image_count} processed images and updated tools")

            # If uploaded images exist, call BFL tool directly for reliability
            if processed_images and processed_images.get('success_count', 0) > 0:
                # Extract first base image
                base_images = processed_images.get('base_images') or []
                base_b64 = base_images[0] if base_images else None

                # Parse inline parameter hints like [provider=bfl, bfl_model=pro, aspect_ratio=3:2]
                import re
                hints = {}
                for m in re.finditer(r"\[(.*?)\]", enhanced_input):
                    content = m.group(1)
                    parts = [p.strip() for p in content.split(',') if '=' in p]
                    for p in parts:
                        k, v = [x.strip() for x in p.split('=', 1)]
                        hints[k] = v

                provider = hints.get('provider', 'bfl')
                if provider == 'bfl':
                    model_variant = hints.get('bfl_model') or hints.get('model_variant') or 'pro'
                    aspect_ratio = hints.get('aspect_ratio')
                    output_format = hints.get('output_format') or 'png'

                    # Strip bracketed hints from prompt text
                    prompt_text = re.sub(r"\s*\[[^\]]*\]", "", enhanced_input).strip()
                    tool_result = self.bfl_tool._run(
                        prompt=prompt_text,
                        image_b64=base_b64,
                        aspect_ratio=aspect_ratio,
                        output_format=output_format,
                        model_variant=model_variant,
                    )
                    logger.info("Processed request via FluxKontextTool directly (with base image)")
                    return tool_result

            # Otherwise, use the LLM agent toolchain
            agent_input = {
                "input": enhanced_input,
                "chat_history": chat_history or [],
            }

            result = self.agent_executor.invoke(agent_input)
            response = result.get("output", "I'm sorry, I couldn't process that request.")
            
            logger.info("Successfully processed user input")
            return response
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)
            return f"I apologize, but I encountered an error: {error_msg}"
    
    def stream_response(self, user_input: str, chat_history: List[BaseMessage] = None):
        """
        Stream the agent's response (for future implementation).
        
        Args:
            user_input: The user's message/request
            chat_history: Optional list of previous messages
            
        Yields:
            str: Chunks of the agent's response
        """
        # For now, return the full response
        # In the future, this could be implemented with streaming
        response = self.invoke(user_input, chat_history)
        yield response
    
    def test_agent(self) -> bool:
        """
        Test the agent with a simple interaction.
        
        Returns:
            bool: True if test successful, False otherwise
        """
        try:
            # Test basic conversation
            response1 = self.invoke("Hello! How are you?")
            if not response1:
                return False
            
            # Test image generation
            response2 = self.invoke("Please generate an image of a sunset over a mountain lake [provider=bfl]")
            if not response2 or "Error" in response2:
                return False
            
            logger.info("Agent test passed")
            return True
            
        except Exception as e:
            logger.error(f"Agent test failed: {str(e)}")
            return False

# Test function for standalone usage
def test_agent():
    """Test the complete agent functionality."""
    try:
        agent = ImageGenerationAgent()
        
        if agent.test_agent():
            print("✓ Agent test passed")
            return True
        else:
            print("✗ Agent test failed")
            return False
    except Exception as e:
        print(f"✗ Agent test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    test_agent()
