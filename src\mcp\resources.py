from typing import Any, Dict, List
import os
import base64
from urllib.parse import urlparse

from config.settings import Settings


ROOT_DIR = Settings.GENERATED_IMAGES_DIR if hasattr(Settings, 'GENERATED_IMAGES_DIR') else 'generated_images'


def list_resources() -> Dict[str, Any]:
    """Return a single resource template describing generated images."""
    return {
        "resources": [
            {
                "uri": f"file://{os.path.abspath(ROOT_DIR)}",
                "name": "Generated Images Root",
                "description": "All images generated by the image tools",
                "mimeType": "inode/directory",
            }
        ],
        # Optional templates could be added if your SDK expects uriTemplate structures
    }


def read_resource(uri: str) -> Dict[str, Any]:
    """Read a resource (file in generated_images)."""
    parsed = urlparse(uri)
    if parsed.scheme != 'file':
        raise ValueError("Only file:// URIs are supported for resources.")

    path = parsed.path
    if os.name == 'nt' and path.startswith('/'):
        # On Windows, file:///C:/path -> /C:/path
        path = path.lstrip('/')

    abs_root = os.path.abspath(ROOT_DIR)
    abs_path = os.path.abspath(path)

    # Prevent directory traversal
    if not abs_path.startswith(abs_root):
        raise ValueError("Access outside generated images directory is forbidden.")

    if not os.path.isfile(abs_path):
        raise FileNotFoundError(abs_path)

    mime = _guess_mime(abs_path)
    with open(abs_path, 'rb') as f:
        data = f.read()

    return {
        "contents": [
            {
                "uri": uri,
                "mimeType": mime,
                "blob": base64.b64encode(data).decode('utf-8')
            }
        ]
    }


def _guess_mime(path: str) -> str:
    p = path.lower()
    if p.endswith('.png'):
        return 'image/png'
    if p.endswith('.jpg') or p.endswith('.jpeg'):
        return 'image/jpeg'
    if p.endswith('.webp'):
        return 'image/webp'
    return 'application/octet-stream'
