"""
FluxKontextTool integrates Black Forest Labs FLUX.1 Kontext (pro/max) for contextual image editing/generation.

Behavior:
- Submits a task to BFL API with prompt and optional base image (base64 or URL).
- Polls the result endpoint until Ready/Error/Moderated.
- Saves resulting image(s) into generated_images with a timestamped filename.
- Returns a user-friendly text summary; errors are returned as text, not exceptions.
"""
from typing import Optional, Type, Dict, Any, Tuple
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
from config.settings import Settings
import logging
import os
import base64
from datetime import datetime
import time
import re
import mimetypes

try:
    import requests  # type: ignore
except Exception:  # pragma: no cover
    requests = None  # type: ignore


logger = logging.getLogger(__name__)


class FluxKontextInput(BaseModel):
    prompt: str = Field(description="Detailed text prompt describing the edit/generation")
    image_b64: Optional[str] = Field(default=None, description="Base64 image data (no data: prefix)")
    image_uri: Optional[str] = Field(default=None, description="Local file path or http(s) URL to an image")
    aspect_ratio: Optional[str] = Field(default=None, description="Aspect ratio like '1:1', '3:2', '2:3'")
    seed: Optional[int] = Field(default=None, description="Optional seed for reproducibility")
    output_format: Optional[str] = Field(default="png", description="'jpeg' or 'png'")
    safety_tolerance: Optional[int] = Field(default=2, description="0..6; higher = more tolerant")
    prompt_upsampling: Optional[bool] = Field(default=False, description="If true, upsample prompt")
    webhook_url: Optional[str] = None
    webhook_secret: Optional[str] = None
    model_variant: Optional[str] = Field(default="pro", description="'pro' or 'max'")


class FluxKontextTool(BaseTool):
    name: str = "FluxKontextTool"
    description: str = (
        "Generate/edit images via Black Forest Labs FLUX.1 Kontext (pro/max). "
        "Provide a prompt and optional base image (image_b64 or image_uri)."
    )
    args_schema: Type[BaseModel] = FluxKontextInput

    def __init__(self):
        super().__init__()

    # --- helpers ---
    def _read_file_to_base64(self, path: str) -> Optional[str]:
        try:
            with open(path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to read image file '{path}': {e}")
            return None

    def _strip_data_url(self, b64_or_dataurl: str) -> str:
        if not b64_or_dataurl:
            return b64_or_dataurl
        # Remove data URL prefix if present
        if b64_or_dataurl.startswith('data:'):
            try:
                return b64_or_dataurl.split(',')[1]
            except Exception:
                return b64_or_dataurl
        return b64_or_dataurl

    def _choose_endpoint(self, variant: str) -> str:
        base = "https://api.bfl.ai/v1"
        return f"{base}/flux-kontext-max" if (variant or '').lower() == 'max' else f"{base}/flux-kontext-pro"

    def _guess_mime_from_path(self, path: Optional[str]) -> str:
        if not path:
            return 'image/png'
        mime, _ = mimetypes.guess_type(path)
        if mime and mime.startswith('image/'):
            return mime
        # Fallback to png
        return 'image/png'

    def _prepare_input_image(self, image_b64: Optional[str], image_uri: Optional[str]) -> Tuple[Optional[str], Optional[str]]:
        """Prepare both raw base64 and data URL forms for the input image.

        Returns (raw_b64, data_url). If the image_uri is an http(s) URL, both
        values will be None for raw_b64 and the data_url will contain the URL
        (Kontext accepts URLs too via input_image).
        """
        # If explicit base64 is provided
        if image_b64:
            # If already a data URL, construct raw form and keep original as data URL
            if image_b64.startswith('data:'):
                raw = self._strip_data_url(image_b64)
                data_url = image_b64
                return raw, data_url
            # Otherwise, we have raw b64; infer mime from uri (if available) or default to png
            raw = image_b64
            mime = self._guess_mime_from_path(image_uri)
            data_url = f"data:{mime};base64,{raw}"
            return raw, data_url

        # No base64 provided; try URI
        if not image_uri:
            return None, None
        if image_uri.startswith('http://') or image_uri.startswith('https://'):
            # URL passthrough supported by Kontext
            return None, image_uri
        # assume local path
        abs_path = os.path.abspath(image_uri)
        if os.path.isfile(abs_path):
            raw = self._read_file_to_base64(abs_path)
            if not raw:
                return None, None
            mime = self._guess_mime_from_path(abs_path)
            data_url = f"data:{mime};base64,{raw}"
            return raw, data_url
        return None, None

    def _save_image_bytes(self, data: bytes, prompt: str, variant: str, ext: str) -> str:
        os.makedirs(Settings.GENERATED_IMAGES_DIR, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        snippet = re.sub(r"[^A-Za-z0-9_-]+", "_", prompt[:30]).strip('_') or "image"
        fname = f"bfl_kontext_{(variant or 'pro').lower()}_{timestamp}_{snippet}.{ext}"
        path = os.path.join(Settings.GENERATED_IMAGES_DIR, fname)
        with open(path, 'wb') as f:
            f.write(data)
        return os.path.abspath(path)

    def _save_result_payload(self, result: Dict[str, Any], prompt: str, variant: str, output_format: str) -> Optional[str]:
        # Handle base64 or URL outputs; prefer base64 if present
        # Common shapes: { images: [ { b64: "..." } ] } or { result: { b64: ... } } etc.
        try:
            img_b64 = None
            img_url = None

            # Explicit key checks based on BFL docs
            # - result.sample: signed URL
            # - result.image / result.output: sometimes used
            # - samples[0], images[0]
            def get_explicit(result_obj: Dict[str, Any]) -> Optional[str]:
                if not isinstance(result_obj, dict):
                    return None
                # Direct URL keys
                for k in ("sample", "image", "output", "url"):
                    v = result_obj.get(k)
                    if isinstance(v, str) and v.startswith("http"):
                        return v
                # Base64 keys
                for k in ("b64", "base64", "image_base64"):
                    v = result_obj.get(k)
                    if isinstance(v, str) and len(v) > 100:
                        return v
                # Arrays
                for arr_key in ("samples", "images", "outputs"):
                    arr = result_obj.get(arr_key)
                    if isinstance(arr, list) and arr:
                        first = arr[0]
                        if isinstance(first, str):
                            return first
                        if isinstance(first, dict):
                            # recurse into first
                            return get_explicit(first)
                return None

            # Heuristics: search for base64
            def find_b64(d: Any) -> Optional[str]:
                if isinstance(d, dict):
                    for k, v in d.items():
                        if isinstance(v, str) and len(v) > 100 and re.match(r"^[A-Za-z0-9+/=]+$", v[:120]):
                            # likely base64
                            return v
                        found = find_b64(v)
                        if found:
                            return found
                elif isinstance(d, list):
                    for it in d:
                        found = find_b64(it)
                        if found:
                            return found
                return None

            def find_url(d: Any) -> Optional[str]:
                if isinstance(d, dict):
                    for k, v in d.items():
                        # Accept any http(s) URL; signed URLs may not include file extensions
                        if isinstance(v, str) and v.startswith('http'):
                            return v
                        found = find_url(v)
                        if found:
                            return found
                elif isinstance(d, list):
                    for it in d:
                        found = find_url(it)
                        if found:
                            return found
                return None

            # Try explicit keys first
            explicit = get_explicit(result)
            if explicit:
                if explicit.startswith('http'):
                    img_url = explicit
                else:
                    img_b64 = explicit

            if not img_b64 and not img_url:
                img_b64 = find_b64(result)
            if img_b64:
                data = base64.b64decode(self._strip_data_url(img_b64))
                ext = 'png' if output_format == 'png' else 'jpg'
                return self._save_image_bytes(data, prompt, variant, ext)

            if not img_url:
                img_url = find_url(result)
            if img_url and requests is not None:
                resp = requests.get(img_url, timeout=30)
                resp.raise_for_status()
                # Guess extension
                ctype = resp.headers.get('content-type', '')
                ext = 'png' if 'png' in ctype or output_format == 'png' else 'jpg'
                return self._save_image_bytes(resp.content, prompt, variant, ext)

            return None
        except Exception as e:
            logger.error(f"Failed to save result payload: {e}")
            return None

    def _submit(self, payload: Dict[str, Any], headers: Dict[str, str], endpoint: str) -> Dict[str, Any]:
        if requests is None:
            return {"error": "requests library not installed"}
        try:
            r = requests.post(endpoint, json=payload, headers=headers, timeout=30)
            if r.status_code >= 400:
                return {"error": f"HTTP {r.status_code}: {r.text}"}
            return r.json()
        except Exception as e:
            return {"error": str(e)}

    def _poll(self, polling_url: str, headers: Dict[str, str], max_wait_s: int = 120) -> Dict[str, Any]:
        if requests is None:
            return {"error": "requests library not installed"}
        start = time.time()
        last_status = None
        while time.time() - start < max_wait_s:
            try:
                pr = requests.get(polling_url, headers=headers, timeout=30)
                if pr.status_code >= 400:
                    return {"error": f"Poll HTTP {pr.status_code}: {pr.text}"}
                data = pr.json()
                status = str(data.get('status') or data.get('state') or '').strip()
                last_status = status
                if status in ("Ready", "Error", "Request Moderated", "Content Moderated"):
                    return data
                # Optional: include preview/progress in logs
                time.sleep(1.5)
            except Exception as e:
                last_status = f"poll_error: {e}"
                time.sleep(2)
        return {"error": f"Polling timeout after {max_wait_s}s", "status": last_status}

    def _run(
        self,
        prompt: str,
        image_b64: Optional[str] = None,
        image_uri: Optional[str] = None,
        aspect_ratio: Optional[str] = None,
        seed: Optional[int] = None,
        output_format: Optional[str] = "png",
        safety_tolerance: Optional[int] = 2,
        prompt_upsampling: Optional[bool] = False,
        webhook_url: Optional[str] = None,
        webhook_secret: Optional[str] = None,
        model_variant: Optional[str] = "pro",
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        # Validate key
        if not Settings.BFL_API_KEY:
            return "Error: BFL_API_KEY is not set. Please configure it to use FLUX.1 Kontext."

        # Resolve aspect ratio
        aspect = aspect_ratio or Settings.BFL_ASPECT_DEFAULT
        # Map legacy sizes if passed accidentally
        if aspect in Settings.SIZE_TO_ASPECT:
            aspect = Settings.SIZE_TO_ASPECT[aspect]

        # Resolve/prepare input image in both raw and data URL forms
        raw_b64, data_url = self._prepare_input_image(image_b64, image_uri)

        # Build payload
        payload: Dict[str, Any] = {
            "prompt": prompt,
            "aspect_ratio": aspect,
            "output_format": (output_format or "png"),
            "safety_tolerance": safety_tolerance if safety_tolerance is not None else 2,
            "prompt_upsampling": bool(prompt_upsampling),
        }
        # Prefer data URL form when we have a local/base64 image; otherwise pass URL directly.
        if data_url:
            payload["input_image"] = data_url
        elif raw_b64:
            payload["input_image"] = raw_b64
        if seed is not None:
            payload["seed"] = seed
        if webhook_url:
            payload["webhook_url"] = webhook_url
        if webhook_secret:
            payload["webhook_secret"] = webhook_secret

        variant = (model_variant or "pro").lower()
        endpoint = self._choose_endpoint(variant)
        headers = {"x-key": Settings.BFL_API_KEY}

        submit = self._submit(payload, headers, endpoint)
        if "error" in submit:
            # Retry once with alternate image encoding (raw base64) if the first attempt used data URL
            # Some environments may prefer raw base64 without data: prefix
            if data_url and raw_b64:
                alt_payload = dict(payload)
                alt_payload["input_image"] = raw_b64
                submit_alt = self._submit(alt_payload, headers, endpoint)
                if "error" not in submit_alt:
                    submit = submit_alt
                else:
                    return f"❌ BFL submit error: {submit['error']} | Alt retry error: {submit_alt['error']}"
            else:
                return f"❌ BFL submit error: {submit['error']}"

        task_id = submit.get("id") or submit.get("task_id") or "unknown"
        polling_url = submit.get("polling_url") or f"https://api.bfl.ai/v1/get_result?id={task_id}"

        poll = self._poll(polling_url, headers)
        if "error" in poll:
            return f"❌ BFL poll error: {poll['error']} (task: {task_id})"

        status = str(poll.get("status") or poll.get("state") or "").strip()
        if status == "Ready":
            result = poll.get("result") or poll
            saved = self._save_result_payload(result, prompt, variant, output_format or "png")
            if saved:
                return (
                    f"✅ FLUX.1 Kontext {variant} completed.\n"
                    f"📝 Prompt: {prompt}\n"
                    f"📐 Aspect: {aspect}\n"
                    f"💾 Saved to: {saved}\n"
                    f"🆔 Task ID: {task_id}"
                )
            return (
                f"⚠️ Completed but no image could be saved from result payload.\n"
                f"Status: {status} | Task: {task_id}"
            )
        elif status in ("Request Moderated", "Content Moderated"):
            details = poll.get("details") or poll.get("message") or ""
            return (
                f"🚫 {status}. Your request was moderated by BFL.\n"
                f"Reason: {details}\n"
                f"Task ID: {task_id}"
            )
        elif status == "Error":
            details = poll.get("details") or poll.get("message") or "Unknown error"
            return f"❌ BFL task failed: {details} (task: {task_id})"
        else:
            return f"❌ Unexpected status '{status}' for task {task_id}."

    async def _arun(self, *args, **kwargs) -> str:
        # Use sync implementation
        return self._run(*args, **kwargs)
