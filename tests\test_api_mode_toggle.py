#!/usr/bin/env python3
"""
Test script to verify that the API mode toggle is working correctly.
Tests both the UI changes and the parameter handling.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_html_ui_api_mode_toggle():
    """Test that the HTML UI includes the new API mode toggle."""
    print("🧪 Testing HTML UI API mode toggle...")
    
    try:
        # Read the HTML file and check for our changes
        with open('templates/chat.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the new API Mode section
        if "🔧 API Mode" in content:
            print("✅ API Mode section found in chat.html")
        else:
            print("❌ API Mode section missing from chat.html")
            return False
        
        # Check for the API mode select element
        if 'id="apiMode"' in content:
            print("✅ apiMode select element found in chat.html")
        else:
            print("❌ apiMode select element missing from chat.html")
            return False
        
        # Check for the options
        if "Responses API (Default)" in content and "Legacy API (No Auto-Enhancement)" in content:
            print("✅ API mode options found in chat.html")
        else:
            print("❌ API mode options missing from chat.html")
            return False
        
        # Check for the info section
        if 'id="apiModeInfo"' in content:
            print("✅ apiModeInfo element found in chat.html")
        else:
            print("❌ apiModeInfo element missing from chat.html")
            return False
        
        # Check for the JavaScript function
        if "updateApiModeInfo" in content:
            print("✅ updateApiModeInfo function found in chat.html")
        else:
            print("❌ updateApiModeInfo function missing from chat.html")
            return False
        
        # Check for API mode parameter handling
        if "params.api_mode = apiMode" in content:
            print("✅ API mode parameter handling found in JavaScript")
        else:
            print("❌ API mode parameter handling missing from JavaScript")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ HTML UI API mode toggle test failed: {str(e)}")
        return False

def test_web_app_api_mode_handling():
    """Test that the web app handles API mode parameters correctly."""
    print("\n🧪 Testing web app API mode handling...")
    
    try:
        # Read the web_app.py file and check for our changes
        with open('web_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for API mode parameter extraction
        if "api_mode = parameters.get('api_mode', 'responses')" in content:
            print("✅ API mode parameter extraction found in web_app.py")
        else:
            print("❌ API mode parameter extraction missing from web_app.py")
            return False
        
        # Check for legacy mode instruction
        if "Use OpenAIImageGenerator or OpenAIImageEditor tools only" in content:
            print("✅ Legacy mode instruction found in web_app.py")
        else:
            print("❌ Legacy mode instruction missing from web_app.py")
            return False
        
        # Check for responses mode instruction
        if "Use ResponsesImageTool for advanced features" in content:
            print("✅ Responses mode instruction found in web_app.py")
        else:
            print("❌ Responses mode instruction missing from web_app.py")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Web app API mode handling test failed: {str(e)}")
        return False

def test_agent_api_mode_instructions():
    """Test that the agent system prompt includes API mode instructions."""
    print("\n🧪 Testing agent API mode instructions...")
    
    try:
        # Read the agent file and check for our changes
        with open('src/agent.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for dynamic tool selection
        if "DYNAMIC TOOL SELECTION: User-Controlled API Mode" in content:
            print("✅ Dynamic tool selection found in agent.py")
        else:
            print("❌ Dynamic tool selection missing from agent.py")
            return False
        
        # Check for tool selection rules
        if "WHEN USER SPECIFIES" in content:
            print("✅ Tool selection rules found in agent.py")
        else:
            print("❌ Tool selection rules missing from agent.py")
            return False
        
        # Check for conversation examples
        if "Legacy API Mode:" in content:
            print("✅ Legacy API mode example found in agent.py")
        else:
            print("❌ Legacy API mode example missing from agent.py")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent API mode instructions test failed: {str(e)}")
        return False

def test_css_styling():
    """Test that the CSS styling for API mode info is present."""
    print("\n🧪 Testing CSS styling for API mode...")
    
    try:
        # Read the HTML file and check for CSS
        with open('templates/chat.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for API mode info styling
        if ".api-mode-info" in content:
            print("✅ API mode info CSS styling found")
        else:
            print("❌ API mode info CSS styling missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ CSS styling test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting API Mode Toggle Tests...")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: HTML UI API mode toggle
    if not test_html_ui_api_mode_toggle():
        all_tests_passed = False
    
    # Test 2: Web app API mode handling
    if not test_web_app_api_mode_handling():
        all_tests_passed = False
    
    # Test 3: Agent API mode instructions
    if not test_agent_api_mode_instructions():
        all_tests_passed = False
    
    # Test 4: CSS styling
    if not test_css_styling():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED! API mode toggle is working correctly.")
        print("\n📋 Summary of verified features:")
        print("✅ UI toggle between Responses API and Legacy API")
        print("✅ Dynamic info display based on selected mode")
        print("✅ Parameter handling in web app")
        print("✅ Agent instructions for tool selection")
        print("✅ CSS styling for the new controls")
        print("\n🎯 User Experience:")
        print("• Default: Responses API with automatic prompt enhancement")
        print("• Toggle to Legacy: Direct API with exact prompt preservation")
        return True
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
