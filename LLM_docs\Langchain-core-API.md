Title: 0.3.65 — 🦜🔗 LangChain documentation

URL Source: https://python.langchain.com/api_reference/core/index.html

Published Time: Sat, 14 Jun 2025 22:20:37 GMT

Markdown Content:
`langchain-core` defines the base abstractions for the LangChain ecosystem.

The interfaces for core components like chat models, LLMs, vector stores, retrievers, and more are defined here. The universal invocation protocol (Runnables) along with a syntax for combining components (LangChain Expression Language) are also defined here.

No third-party integrations are defined here. The dependencies are kept purposefully very lightweight.

[agents](https://python.langchain.com/api_reference/core/agents.html#langchain-core-agents)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-agents "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[beta](https://python.langchain.com/api_reference/core/beta.html#langchain-core-beta)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-beta "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

**Functions**

[caches](https://python.langchain.com/api_reference/core/caches.html#langchain-core-caches)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-caches "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[callbacks](https://python.langchain.com/api_reference/core/callbacks.html#langchain-core-callbacks)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-callbacks "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`callbacks.base.AsyncCallbackHandler`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.AsyncCallbackHandler.html#langchain_core.callbacks.base.AsyncCallbackHandler "langchain_core.callbacks.base.AsyncCallbackHandler")()Async callback handler for LangChain.
[`callbacks.base.BaseCallbackHandler`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.BaseCallbackHandler.html#langchain_core.callbacks.base.BaseCallbackHandler "langchain_core.callbacks.base.BaseCallbackHandler")()Base callback handler for LangChain.
[`callbacks.base.BaseCallbackManager`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.BaseCallbackManager.html#langchain_core.callbacks.base.BaseCallbackManager "langchain_core.callbacks.base.BaseCallbackManager")(handlers)Base callback manager for LangChain.
[`callbacks.base.CallbackManagerMixin`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.CallbackManagerMixin.html#langchain_core.callbacks.base.CallbackManagerMixin "langchain_core.callbacks.base.CallbackManagerMixin")()Mixin for callback manager.
[`callbacks.base.ChainManagerMixin`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.ChainManagerMixin.html#langchain_core.callbacks.base.ChainManagerMixin "langchain_core.callbacks.base.ChainManagerMixin")()Mixin for chain callbacks.
[`callbacks.base.LLMManagerMixin`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.LLMManagerMixin.html#langchain_core.callbacks.base.LLMManagerMixin "langchain_core.callbacks.base.LLMManagerMixin")()Mixin for LLM callbacks.
[`callbacks.base.RetrieverManagerMixin`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.RetrieverManagerMixin.html#langchain_core.callbacks.base.RetrieverManagerMixin "langchain_core.callbacks.base.RetrieverManagerMixin")()Mixin for Retriever callbacks.
[`callbacks.base.RunManagerMixin`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.RunManagerMixin.html#langchain_core.callbacks.base.RunManagerMixin "langchain_core.callbacks.base.RunManagerMixin")()Mixin for run manager.
[`callbacks.base.ToolManagerMixin`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.base.ToolManagerMixin.html#langchain_core.callbacks.base.ToolManagerMixin "langchain_core.callbacks.base.ToolManagerMixin")()Mixin for tool callbacks.
[`callbacks.file.FileCallbackHandler`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.file.FileCallbackHandler.html#langchain_core.callbacks.file.FileCallbackHandler "langchain_core.callbacks.file.FileCallbackHandler")(filename)Callback Handler that writes to a file.
[`callbacks.manager.AsyncCallbackManager`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.AsyncCallbackManager.html#langchain_core.callbacks.manager.AsyncCallbackManager "langchain_core.callbacks.manager.AsyncCallbackManager")(handlers)Async callback manager that handles callbacks from LangChain.
[`callbacks.manager.AsyncCallbackManagerForChainGroup`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.AsyncCallbackManagerForChainGroup.html#langchain_core.callbacks.manager.AsyncCallbackManagerForChainGroup "langchain_core.callbacks.manager.AsyncCallbackManagerForChainGroup")(...)Async callback manager for the chain group.
[`callbacks.manager.AsyncCallbackManagerForChainRun`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.AsyncCallbackManagerForChainRun.html#langchain_core.callbacks.manager.AsyncCallbackManagerForChainRun "langchain_core.callbacks.manager.AsyncCallbackManagerForChainRun")(*,...)Async callback manager for chain run.
[`callbacks.manager.AsyncCallbackManagerForLLMRun`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.AsyncCallbackManagerForLLMRun.html#langchain_core.callbacks.manager.AsyncCallbackManagerForLLMRun "langchain_core.callbacks.manager.AsyncCallbackManagerForLLMRun")(*,...)Async callback manager for LLM run.
[`callbacks.manager.AsyncCallbackManagerForRetrieverRun`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.AsyncCallbackManagerForRetrieverRun.html#langchain_core.callbacks.manager.AsyncCallbackManagerForRetrieverRun "langchain_core.callbacks.manager.AsyncCallbackManagerForRetrieverRun")(*,...)Async callback manager for retriever run.
[`callbacks.manager.AsyncCallbackManagerForToolRun`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.AsyncCallbackManagerForToolRun.html#langchain_core.callbacks.manager.AsyncCallbackManagerForToolRun "langchain_core.callbacks.manager.AsyncCallbackManagerForToolRun")(*,...)Async callback manager for tool run.
[`callbacks.manager.AsyncParentRunManager`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.AsyncParentRunManager.html#langchain_core.callbacks.manager.AsyncParentRunManager "langchain_core.callbacks.manager.AsyncParentRunManager")(*,...)Async Parent Run Manager.
[`callbacks.manager.AsyncRunManager`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.AsyncRunManager.html#langchain_core.callbacks.manager.AsyncRunManager "langchain_core.callbacks.manager.AsyncRunManager")(*,run_id,...)Async Run Manager.
[`callbacks.manager.BaseRunManager`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.BaseRunManager.html#langchain_core.callbacks.manager.BaseRunManager "langchain_core.callbacks.manager.BaseRunManager")(*,run_id,...)Base class for run manager (a bound callback manager).
[`callbacks.manager.CallbackManager`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.CallbackManager.html#langchain_core.callbacks.manager.CallbackManager "langchain_core.callbacks.manager.CallbackManager")(handlers)Callback manager for LangChain.
[`callbacks.manager.CallbackManagerForChainGroup`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.CallbackManagerForChainGroup.html#langchain_core.callbacks.manager.CallbackManagerForChainGroup "langchain_core.callbacks.manager.CallbackManagerForChainGroup")(...)Callback manager for the chain group.
[`callbacks.manager.CallbackManagerForChainRun`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.CallbackManagerForChainRun.html#langchain_core.callbacks.manager.CallbackManagerForChainRun "langchain_core.callbacks.manager.CallbackManagerForChainRun")(*,...)Callback manager for chain run.
[`callbacks.manager.CallbackManagerForLLMRun`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.CallbackManagerForLLMRun.html#langchain_core.callbacks.manager.CallbackManagerForLLMRun "langchain_core.callbacks.manager.CallbackManagerForLLMRun")(*,...)Callback manager for LLM run.
[`callbacks.manager.CallbackManagerForRetrieverRun`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.CallbackManagerForRetrieverRun.html#langchain_core.callbacks.manager.CallbackManagerForRetrieverRun "langchain_core.callbacks.manager.CallbackManagerForRetrieverRun")(*,...)Callback manager for retriever run.
[`callbacks.manager.CallbackManagerForToolRun`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.CallbackManagerForToolRun.html#langchain_core.callbacks.manager.CallbackManagerForToolRun "langchain_core.callbacks.manager.CallbackManagerForToolRun")(*,...)Callback manager for tool run.
[`callbacks.manager.ParentRunManager`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.ParentRunManager.html#langchain_core.callbacks.manager.ParentRunManager "langchain_core.callbacks.manager.ParentRunManager")(*,...[,...])Sync Parent Run Manager.
[`callbacks.manager.RunManager`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.RunManager.html#langchain_core.callbacks.manager.RunManager "langchain_core.callbacks.manager.RunManager")(*,run_id,...)Sync Run Manager.
[`callbacks.stdout.StdOutCallbackHandler`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.stdout.StdOutCallbackHandler.html#langchain_core.callbacks.stdout.StdOutCallbackHandler "langchain_core.callbacks.stdout.StdOutCallbackHandler")([color])Callback Handler that prints to std out.
[`callbacks.streaming_stdout.StreamingStdOutCallbackHandler`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.streaming_stdout.StreamingStdOutCallbackHandler.html#langchain_core.callbacks.streaming_stdout.StreamingStdOutCallbackHandler "langchain_core.callbacks.streaming_stdout.StreamingStdOutCallbackHandler")()Callback handler for streaming.
[`callbacks.usage.UsageMetadataCallbackHandler`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.usage.UsageMetadataCallbackHandler.html#langchain_core.callbacks.usage.UsageMetadataCallbackHandler "langchain_core.callbacks.usage.UsageMetadataCallbackHandler")()Callback Handler that tracks AIMessage.usage_metadata.

**Functions**

[`callbacks.manager.adispatch_custom_event`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.adispatch_custom_event.html#langchain_core.callbacks.manager.adispatch_custom_event "langchain_core.callbacks.manager.adispatch_custom_event")(...)Dispatch an adhoc event to the handlers.
[`callbacks.manager.ahandle_event`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.ahandle_event.html#langchain_core.callbacks.manager.ahandle_event "langchain_core.callbacks.manager.ahandle_event")(handlers,...)Async generic event handler for AsyncCallbackManager.
[`callbacks.manager.atrace_as_chain_group`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.atrace_as_chain_group.html#langchain_core.callbacks.manager.atrace_as_chain_group "langchain_core.callbacks.manager.atrace_as_chain_group")(...)Get an async callback manager for a chain group in a context manager.
[`callbacks.manager.dispatch_custom_event`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.dispatch_custom_event.html#langchain_core.callbacks.manager.dispatch_custom_event "langchain_core.callbacks.manager.dispatch_custom_event")(...)Dispatch an adhoc event.
[`callbacks.manager.handle_event`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.handle_event.html#langchain_core.callbacks.manager.handle_event "langchain_core.callbacks.manager.handle_event")(handlers,...)Generic event handler for CallbackManager.
[`callbacks.manager.shielded`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.shielded.html#langchain_core.callbacks.manager.shielded "langchain_core.callbacks.manager.shielded")(func)Makes so an awaitable method is always shielded from cancellation.
[`callbacks.manager.trace_as_chain_group`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.manager.trace_as_chain_group.html#langchain_core.callbacks.manager.trace_as_chain_group "langchain_core.callbacks.manager.trace_as_chain_group")(...)Get a callback manager for a chain group in a context manager.
[`callbacks.usage.get_usage_metadata_callback`](https://python.langchain.com/api_reference/core/callbacks/langchain_core.callbacks.usage.get_usage_metadata_callback.html#langchain_core.callbacks.usage.get_usage_metadata_callback "langchain_core.callbacks.usage.get_usage_metadata_callback")([name])Get usage metadata callback.

[chat_history](https://python.langchain.com/api_reference/core/chat_history.html#langchain-core-chat-history)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-chat-history "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[chat_loaders](https://python.langchain.com/api_reference/core/chat_loaders.html#langchain-core-chat-loaders)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-chat-loaders "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[chat_sessions](https://python.langchain.com/api_reference/core/chat_sessions.html#langchain-core-chat-sessions)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-chat-sessions "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[document_loaders](https://python.langchain.com/api_reference/core/document_loaders.html#langchain-core-document-loaders)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-document-loaders "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[documents](https://python.langchain.com/api_reference/core/documents.html#langchain-core-documents)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-documents "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[embeddings](https://python.langchain.com/api_reference/core/embeddings.html#langchain-core-embeddings)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-embeddings "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[example_selectors](https://python.langchain.com/api_reference/core/example_selectors.html#langchain-core-example-selectors)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-example-selectors "Link to this heading")
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

**Functions**

[exceptions](https://python.langchain.com/api_reference/core/exceptions.html#langchain-core-exceptions)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-exceptions "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

**Functions**

[globals](https://python.langchain.com/api_reference/core/globals.html#langchain-core-globals)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-globals "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Functions**

[`globals.get_debug`](https://python.langchain.com/api_reference/core/globals/langchain_core.globals.get_debug.html#langchain_core.globals.get_debug "langchain_core.globals.get_debug")()Get the value of the debug global setting.
[`globals.get_llm_cache`](https://python.langchain.com/api_reference/core/globals/langchain_core.globals.get_llm_cache.html#langchain_core.globals.get_llm_cache "langchain_core.globals.get_llm_cache")()Get the value of the llm_cache global setting.
[`globals.get_verbose`](https://python.langchain.com/api_reference/core/globals/langchain_core.globals.get_verbose.html#langchain_core.globals.get_verbose "langchain_core.globals.get_verbose")()Get the value of the verbose global setting.
[`globals.set_debug`](https://python.langchain.com/api_reference/core/globals/langchain_core.globals.set_debug.html#langchain_core.globals.set_debug "langchain_core.globals.set_debug")(value)Set a new value for the debug global setting.
[`globals.set_llm_cache`](https://python.langchain.com/api_reference/core/globals/langchain_core.globals.set_llm_cache.html#langchain_core.globals.set_llm_cache "langchain_core.globals.set_llm_cache")(value)Set a new LLM cache, overwriting the previous value, if any.
[`globals.set_verbose`](https://python.langchain.com/api_reference/core/globals/langchain_core.globals.set_verbose.html#langchain_core.globals.set_verbose "langchain_core.globals.set_verbose")(value)Set a new value for the verbose global setting.

[indexing](https://python.langchain.com/api_reference/core/indexing.html#langchain-core-indexing)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-indexing "Link to this heading")
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`indexing.api.IndexingException`](https://python.langchain.com/api_reference/core/indexing/langchain_core.indexing.api.IndexingException.html#langchain_core.indexing.api.IndexingException "langchain_core.indexing.api.IndexingException")Raised when an indexing operation fails.
[`indexing.api.IndexingResult`](https://python.langchain.com/api_reference/core/indexing/langchain_core.indexing.api.IndexingResult.html#langchain_core.indexing.api.IndexingResult "langchain_core.indexing.api.IndexingResult")Return a detailed a breakdown of the result of the indexing operation.
[`indexing.base.DeleteResponse`](https://python.langchain.com/api_reference/core/indexing/langchain_core.indexing.base.DeleteResponse.html#langchain_core.indexing.base.DeleteResponse "langchain_core.indexing.base.DeleteResponse")A generic response for delete operation.
[`indexing.base.DocumentIndex`](https://python.langchain.com/api_reference/core/indexing/langchain_core.indexing.base.DocumentIndex.html#langchain_core.indexing.base.DocumentIndex "langchain_core.indexing.base.DocumentIndex")
[`indexing.base.InMemoryRecordManager`](https://python.langchain.com/api_reference/core/indexing/langchain_core.indexing.base.InMemoryRecordManager.html#langchain_core.indexing.base.InMemoryRecordManager "langchain_core.indexing.base.InMemoryRecordManager")(namespace)An in-memory record manager for testing purposes.
[`indexing.base.RecordManager`](https://python.langchain.com/api_reference/core/indexing/langchain_core.indexing.base.RecordManager.html#langchain_core.indexing.base.RecordManager "langchain_core.indexing.base.RecordManager")(namespace)Abstract base class representing the interface for a record manager.
[`indexing.base.UpsertResponse`](https://python.langchain.com/api_reference/core/indexing/langchain_core.indexing.base.UpsertResponse.html#langchain_core.indexing.base.UpsertResponse "langchain_core.indexing.base.UpsertResponse")A generic response for upsert operations.
[`indexing.in_memory.InMemoryDocumentIndex`](https://python.langchain.com/api_reference/core/indexing/langchain_core.indexing.in_memory.InMemoryDocumentIndex.html#langchain_core.indexing.in_memory.InMemoryDocumentIndex "langchain_core.indexing.in_memory.InMemoryDocumentIndex")

**Functions**

[language_models](https://python.langchain.com/api_reference/core/language_models.html#langchain-core-language-models)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-language-models "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`language_models.base.BaseLanguageModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.base.BaseLanguageModel.html#langchain_core.language_models.base.BaseLanguageModel "langchain_core.language_models.base.BaseLanguageModel")Abstract base class for interfacing with language models.
[`language_models.base.LangSmithParams`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.base.LangSmithParams.html#langchain_core.language_models.base.LangSmithParams "langchain_core.language_models.base.LangSmithParams")LangSmith parameters for tracing.
[`language_models.chat_models.BaseChatModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.chat_models.BaseChatModel.html#langchain_core.language_models.chat_models.BaseChatModel "langchain_core.language_models.chat_models.BaseChatModel")Base class for chat models.
[`language_models.chat_models.SimpleChatModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.chat_models.SimpleChatModel.html#langchain_core.language_models.chat_models.SimpleChatModel "langchain_core.language_models.chat_models.SimpleChatModel")Simplified implementation for a chat model to inherit from.
[`language_models.fake.FakeListLLM`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake.FakeListLLM.html#langchain_core.language_models.fake.FakeListLLM "langchain_core.language_models.fake.FakeListLLM")Fake LLM for testing purposes.
[`language_models.fake.FakeListLLMError`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake.FakeListLLMError.html#langchain_core.language_models.fake.FakeListLLMError "langchain_core.language_models.fake.FakeListLLMError")Fake error for testing purposes.
[`language_models.fake.FakeStreamingListLLM`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake.FakeStreamingListLLM.html#langchain_core.language_models.fake.FakeStreamingListLLM "langchain_core.language_models.fake.FakeStreamingListLLM")Fake streaming list LLM for testing purposes.
[`language_models.fake_chat_models.FakeChatModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake_chat_models.FakeChatModel.html#langchain_core.language_models.fake_chat_models.FakeChatModel "langchain_core.language_models.fake_chat_models.FakeChatModel")Fake Chat Model wrapper for testing purposes.
[`language_models.fake_chat_models.FakeListChatModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake_chat_models.FakeListChatModel.html#langchain_core.language_models.fake_chat_models.FakeListChatModel "langchain_core.language_models.fake_chat_models.FakeListChatModel")Fake ChatModel for testing purposes.
[`language_models.fake_chat_models.FakeListChatModelError`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake_chat_models.FakeListChatModelError.html#langchain_core.language_models.fake_chat_models.FakeListChatModelError "langchain_core.language_models.fake_chat_models.FakeListChatModelError")Fake error for testing purposes.
[`language_models.fake_chat_models.FakeMessagesListChatModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake_chat_models.FakeMessagesListChatModel.html#langchain_core.language_models.fake_chat_models.FakeMessagesListChatModel "langchain_core.language_models.fake_chat_models.FakeMessagesListChatModel")Fake ChatModel for testing purposes.
[`language_models.fake_chat_models.GenericFakeChatModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake_chat_models.GenericFakeChatModel.html#langchain_core.language_models.fake_chat_models.GenericFakeChatModel "langchain_core.language_models.fake_chat_models.GenericFakeChatModel")Generic fake chat model that can be used to test the chat model interface.
[`language_models.fake_chat_models.ParrotFakeChatModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.fake_chat_models.ParrotFakeChatModel.html#langchain_core.language_models.fake_chat_models.ParrotFakeChatModel "langchain_core.language_models.fake_chat_models.ParrotFakeChatModel")Generic fake chat model that can be used to test the chat model interface.
[`language_models.llms.BaseLLM`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.llms.BaseLLM.html#langchain_core.language_models.llms.BaseLLM "langchain_core.language_models.llms.BaseLLM")Base LLM abstract interface.
[`language_models.llms.LLM`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.llms.LLM.html#langchain_core.language_models.llms.LLM "langchain_core.language_models.llms.LLM")Simple interface for implementing a custom LLM.

**Functions**

[`language_models.chat_models.agenerate_from_stream`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.chat_models.agenerate_from_stream.html#langchain_core.language_models.chat_models.agenerate_from_stream "langchain_core.language_models.chat_models.agenerate_from_stream")(stream)Async generate from a stream.
[`language_models.chat_models.generate_from_stream`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.chat_models.generate_from_stream.html#langchain_core.language_models.chat_models.generate_from_stream "langchain_core.language_models.chat_models.generate_from_stream")(stream)Generate from a stream.
[`language_models.llms.aget_prompts`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.llms.aget_prompts.html#langchain_core.language_models.llms.aget_prompts "langchain_core.language_models.llms.aget_prompts")(params,...)Get prompts that are already cached.
[`language_models.llms.aupdate_cache`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.llms.aupdate_cache.html#langchain_core.language_models.llms.aupdate_cache "langchain_core.language_models.llms.aupdate_cache")(cache,...)Update the cache and get the LLM output.
[`language_models.llms.create_base_retry_decorator`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.llms.create_base_retry_decorator.html#langchain_core.language_models.llms.create_base_retry_decorator "langchain_core.language_models.llms.create_base_retry_decorator")(...)Create a retry decorator for a given LLM and provided a list of error types.
[`language_models.llms.get_prompts`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.llms.get_prompts.html#langchain_core.language_models.llms.get_prompts "langchain_core.language_models.llms.get_prompts")(params,prompts)Get prompts that are already cached.
[`language_models.llms.update_cache`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.llms.update_cache.html#langchain_core.language_models.llms.update_cache "langchain_core.language_models.llms.update_cache")(cache,...)Update the cache and get the LLM output.

[load](https://python.langchain.com/api_reference/core/load.html#langchain-core-load)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-load "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`load.load.Reviver`](https://python.langchain.com/api_reference/core/load/langchain_core.load.load.Reviver.html#langchain_core.load.load.Reviver "langchain_core.load.load.Reviver")([secrets_map,...])Reviver for JSON objects.
[`load.serializable.BaseSerialized`](https://python.langchain.com/api_reference/core/load/langchain_core.load.serializable.BaseSerialized.html#langchain_core.load.serializable.BaseSerialized "langchain_core.load.serializable.BaseSerialized")Base class for serialized objects.
[`load.serializable.Serializable`](https://python.langchain.com/api_reference/core/load/langchain_core.load.serializable.Serializable.html#langchain_core.load.serializable.Serializable "langchain_core.load.serializable.Serializable")Serializable base class.
[`load.serializable.SerializedConstructor`](https://python.langchain.com/api_reference/core/load/langchain_core.load.serializable.SerializedConstructor.html#langchain_core.load.serializable.SerializedConstructor "langchain_core.load.serializable.SerializedConstructor")Serialized constructor.
[`load.serializable.SerializedNotImplemented`](https://python.langchain.com/api_reference/core/load/langchain_core.load.serializable.SerializedNotImplemented.html#langchain_core.load.serializable.SerializedNotImplemented "langchain_core.load.serializable.SerializedNotImplemented")Serialized not implemented.
[`load.serializable.SerializedSecret`](https://python.langchain.com/api_reference/core/load/langchain_core.load.serializable.SerializedSecret.html#langchain_core.load.serializable.SerializedSecret "langchain_core.load.serializable.SerializedSecret")Serialized secret.

**Functions**

[`load.dump.default`](https://python.langchain.com/api_reference/core/load/langchain_core.load.dump.default.html#langchain_core.load.dump.default "langchain_core.load.dump.default")(obj)Return a default value for an object.
[`load.dump.dumpd`](https://python.langchain.com/api_reference/core/load/langchain_core.load.dump.dumpd.html#langchain_core.load.dump.dumpd "langchain_core.load.dump.dumpd")(obj)Return a dict representation of an object.
[`load.dump.dumps`](https://python.langchain.com/api_reference/core/load/langchain_core.load.dump.dumps.html#langchain_core.load.dump.dumps "langchain_core.load.dump.dumps")(obj,*[,pretty])Return a json string representation of an object.
[`load.load.load`](https://python.langchain.com/api_reference/core/load/langchain_core.load.load.load.html#langchain_core.load.load.load "langchain_core.load.load.load")(obj,*[,secrets_map,...])
[`load.load.loads`](https://python.langchain.com/api_reference/core/load/langchain_core.load.load.loads.html#langchain_core.load.load.loads "langchain_core.load.load.loads")(text,*[,secrets_map,...])
[`load.serializable.to_json_not_implemented`](https://python.langchain.com/api_reference/core/load/langchain_core.load.serializable.to_json_not_implemented.html#langchain_core.load.serializable.to_json_not_implemented "langchain_core.load.serializable.to_json_not_implemented")(obj)Serialize a "not implemented" object.
[`load.serializable.try_neq_default`](https://python.langchain.com/api_reference/core/load/langchain_core.load.serializable.try_neq_default.html#langchain_core.load.serializable.try_neq_default "langchain_core.load.serializable.try_neq_default")(value,...)Try to determine if a value is different from the default.

[messages](https://python.langchain.com/api_reference/core/messages.html#langchain-core-messages)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-messages "Link to this heading")
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`messages.ai.AIMessage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.AIMessage.html#langchain_core.messages.ai.AIMessage "langchain_core.messages.ai.AIMessage")Message from an AI.
[`messages.ai.AIMessageChunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.AIMessageChunk.html#langchain_core.messages.ai.AIMessageChunk "langchain_core.messages.ai.AIMessageChunk")Message chunk from an AI.
[`messages.ai.InputTokenDetails`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.InputTokenDetails.html#langchain_core.messages.ai.InputTokenDetails "langchain_core.messages.ai.InputTokenDetails")Breakdown of input token counts.
[`messages.ai.OutputTokenDetails`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.OutputTokenDetails.html#langchain_core.messages.ai.OutputTokenDetails "langchain_core.messages.ai.OutputTokenDetails")Breakdown of output token counts.
[`messages.ai.UsageMetadata`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.UsageMetadata.html#langchain_core.messages.ai.UsageMetadata "langchain_core.messages.ai.UsageMetadata")Usage metadata for a message, such as token counts.
[`messages.base.BaseMessage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.base.BaseMessage.html#langchain_core.messages.base.BaseMessage "langchain_core.messages.base.BaseMessage")Base abstract message class.
[`messages.base.BaseMessageChunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.base.BaseMessageChunk.html#langchain_core.messages.base.BaseMessageChunk "langchain_core.messages.base.BaseMessageChunk")Message chunk, which can be concatenated with other Message chunks.
[`messages.chat.ChatMessage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.chat.ChatMessage.html#langchain_core.messages.chat.ChatMessage "langchain_core.messages.chat.ChatMessage")Message that can be assigned an arbitrary speaker (i.e. role).
[`messages.chat.ChatMessageChunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.chat.ChatMessageChunk.html#langchain_core.messages.chat.ChatMessageChunk "langchain_core.messages.chat.ChatMessageChunk")Chat Message chunk.
[`messages.content_blocks.Base64ContentBlock`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.content_blocks.Base64ContentBlock.html#langchain_core.messages.content_blocks.Base64ContentBlock "langchain_core.messages.content_blocks.Base64ContentBlock")Content block for inline data from a base64 string.
[`messages.content_blocks.BaseDataContentBlock`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.content_blocks.BaseDataContentBlock.html#langchain_core.messages.content_blocks.BaseDataContentBlock "langchain_core.messages.content_blocks.BaseDataContentBlock")Base class for data content blocks.
[`messages.content_blocks.IDContentBlock`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.content_blocks.IDContentBlock.html#langchain_core.messages.content_blocks.IDContentBlock "langchain_core.messages.content_blocks.IDContentBlock")Content block for data specified by an identifier.
[`messages.content_blocks.PlainTextContentBlock`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.content_blocks.PlainTextContentBlock.html#langchain_core.messages.content_blocks.PlainTextContentBlock "langchain_core.messages.content_blocks.PlainTextContentBlock")Content block for plain text data (e.g., from a document).
[`messages.content_blocks.URLContentBlock`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.content_blocks.URLContentBlock.html#langchain_core.messages.content_blocks.URLContentBlock "langchain_core.messages.content_blocks.URLContentBlock")Content block for data from a URL.
[`messages.function.FunctionMessage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.function.FunctionMessage.html#langchain_core.messages.function.FunctionMessage "langchain_core.messages.function.FunctionMessage")Message for passing the result of executing a tool back to a model.
[`messages.function.FunctionMessageChunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.function.FunctionMessageChunk.html#langchain_core.messages.function.FunctionMessageChunk "langchain_core.messages.function.FunctionMessageChunk")Function Message chunk.
[`messages.human.HumanMessage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.human.HumanMessage.html#langchain_core.messages.human.HumanMessage "langchain_core.messages.human.HumanMessage")Message from a human.
[`messages.human.HumanMessageChunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.human.HumanMessageChunk.html#langchain_core.messages.human.HumanMessageChunk "langchain_core.messages.human.HumanMessageChunk")Human Message chunk.
[`messages.modifier.RemoveMessage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.modifier.RemoveMessage.html#langchain_core.messages.modifier.RemoveMessage "langchain_core.messages.modifier.RemoveMessage")Message responsible for deleting other messages.
[`messages.system.SystemMessage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.system.SystemMessage.html#langchain_core.messages.system.SystemMessage "langchain_core.messages.system.SystemMessage")Message for priming AI behavior.
[`messages.system.SystemMessageChunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.system.SystemMessageChunk.html#langchain_core.messages.system.SystemMessageChunk "langchain_core.messages.system.SystemMessageChunk")System Message chunk.
[`messages.tool.InvalidToolCall`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.InvalidToolCall.html#langchain_core.messages.tool.InvalidToolCall "langchain_core.messages.tool.InvalidToolCall")Allowance for errors made by LLM.
[`messages.tool.ToolCall`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.ToolCall.html#langchain_core.messages.tool.ToolCall "langchain_core.messages.tool.ToolCall")Represents a request to call a tool.
[`messages.tool.ToolCallChunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.ToolCallChunk.html#langchain_core.messages.tool.ToolCallChunk "langchain_core.messages.tool.ToolCallChunk")A chunk of a tool call (e.g., as part of a stream).
[`messages.tool.ToolMessage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.ToolMessage.html#langchain_core.messages.tool.ToolMessage "langchain_core.messages.tool.ToolMessage")Message for passing the result of executing a tool back to a model.
[`messages.tool.ToolMessageChunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.ToolMessageChunk.html#langchain_core.messages.tool.ToolMessageChunk "langchain_core.messages.tool.ToolMessageChunk")Tool Message chunk.
[`messages.tool.ToolOutputMixin`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.ToolOutputMixin.html#langchain_core.messages.tool.ToolOutputMixin "langchain_core.messages.tool.ToolOutputMixin")()Mixin for objects that tools can return directly.

**Functions**

[`messages.ai.add_ai_message_chunks`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.add_ai_message_chunks.html#langchain_core.messages.ai.add_ai_message_chunks "langchain_core.messages.ai.add_ai_message_chunks")(left,*others)Add multiple AIMessageChunks together.
[`messages.ai.add_usage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.add_usage.html#langchain_core.messages.ai.add_usage "langchain_core.messages.ai.add_usage")(left,right)Recursively add two UsageMetadata objects.
[`messages.ai.subtract_usage`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.subtract_usage.html#langchain_core.messages.ai.subtract_usage "langchain_core.messages.ai.subtract_usage")(left,right)Recursively subtract two UsageMetadata objects.
[`messages.base.get_msg_title_repr`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.base.get_msg_title_repr.html#langchain_core.messages.base.get_msg_title_repr "langchain_core.messages.base.get_msg_title_repr")(title,*[,...])Get a title representation for a message.
[`messages.base.merge_content`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.base.merge_content.html#langchain_core.messages.base.merge_content "langchain_core.messages.base.merge_content")(first_content,...)Merge multiple message contents.
[`messages.base.message_to_dict`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.base.message_to_dict.html#langchain_core.messages.base.message_to_dict "langchain_core.messages.base.message_to_dict")(message)Convert a Message to a dictionary.
[`messages.base.messages_to_dict`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.base.messages_to_dict.html#langchain_core.messages.base.messages_to_dict "langchain_core.messages.base.messages_to_dict")(messages)Convert a sequence of Messages to a list of dictionaries.
[`messages.content_blocks.convert_to_openai_data_block`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.content_blocks.convert_to_openai_data_block.html#langchain_core.messages.content_blocks.convert_to_openai_data_block "langchain_core.messages.content_blocks.convert_to_openai_data_block")(block)Format standard data content block to format expected by OpenAI.
[`messages.content_blocks.convert_to_openai_image_block`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.content_blocks.convert_to_openai_image_block.html#langchain_core.messages.content_blocks.convert_to_openai_image_block "langchain_core.messages.content_blocks.convert_to_openai_image_block")(...)Convert image content block to format expected by OpenAI Chat Completions API.
[`messages.content_blocks.is_data_content_block`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.content_blocks.is_data_content_block.html#langchain_core.messages.content_blocks.is_data_content_block "langchain_core.messages.content_blocks.is_data_content_block")(...)Check if the content block is a standard data content block.
[`messages.tool.default_tool_chunk_parser`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.default_tool_chunk_parser.html#langchain_core.messages.tool.default_tool_chunk_parser "langchain_core.messages.tool.default_tool_chunk_parser")(...)Best-effort parsing of tool chunks.
[`messages.tool.default_tool_parser`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.default_tool_parser.html#langchain_core.messages.tool.default_tool_parser "langchain_core.messages.tool.default_tool_parser")(raw_tool_calls)Best-effort parsing of tools.
[`messages.tool.invalid_tool_call`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.invalid_tool_call.html#langchain_core.messages.tool.invalid_tool_call "langchain_core.messages.tool.invalid_tool_call")(*[,name,...])Create an invalid tool call.
[`messages.tool.tool_call`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.tool_call.html#langchain_core.messages.tool.tool_call "langchain_core.messages.tool.tool_call")(*,name,args,id)Create a tool call.
[`messages.tool.tool_call_chunk`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.tool.tool_call_chunk.html#langchain_core.messages.tool.tool_call_chunk "langchain_core.messages.tool.tool_call_chunk")(*[,name,...])Create a tool call chunk.
[`messages.utils.convert_to_messages`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.convert_to_messages.html#langchain_core.messages.utils.convert_to_messages "langchain_core.messages.utils.convert_to_messages")(messages)Convert a sequence of messages to a list of messages.
[`messages.utils.convert_to_openai_messages`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.convert_to_openai_messages.html#langchain_core.messages.utils.convert_to_openai_messages "langchain_core.messages.utils.convert_to_openai_messages")(...)Convert LangChain messages into OpenAI message dicts.
[`messages.utils.count_tokens_approximately`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.count_tokens_approximately.html#langchain_core.messages.utils.count_tokens_approximately "langchain_core.messages.utils.count_tokens_approximately")(...)Approximate the total number of tokens in messages.
[`messages.utils.filter_messages`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.filter_messages.html#langchain_core.messages.utils.filter_messages "langchain_core.messages.utils.filter_messages")([messages])Filter messages based on name, type or id.
[`messages.utils.get_buffer_string`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.get_buffer_string.html#langchain_core.messages.utils.get_buffer_string "langchain_core.messages.utils.get_buffer_string")(messages[,...])Convert a sequence of Messages to strings and concatenate them into one string.
[`messages.utils.merge_message_runs`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.merge_message_runs.html#langchain_core.messages.utils.merge_message_runs "langchain_core.messages.utils.merge_message_runs")([messages])Merge consecutive Messages of the same type.
[`messages.utils.message_chunk_to_message`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.message_chunk_to_message.html#langchain_core.messages.utils.message_chunk_to_message "langchain_core.messages.utils.message_chunk_to_message")(chunk)Convert a message chunk to a message.
[`messages.utils.messages_from_dict`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.messages_from_dict.html#langchain_core.messages.utils.messages_from_dict "langchain_core.messages.utils.messages_from_dict")(messages)Convert a sequence of messages from dicts to Message objects.
[`messages.utils.trim_messages`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.trim_messages.html#langchain_core.messages.utils.trim_messages "langchain_core.messages.utils.trim_messages")([messages])Trim messages to be below a token count.

[output_parsers](https://python.langchain.com/api_reference/core/output_parsers.html#langchain-core-output-parsers)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-output-parsers "Link to this heading")
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`output_parsers.base.BaseGenerationOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.base.BaseGenerationOutputParser.html#langchain_core.output_parsers.base.BaseGenerationOutputParser "langchain_core.output_parsers.base.BaseGenerationOutputParser")Base class to parse the output of an LLM call.
[`output_parsers.base.BaseLLMOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.base.BaseLLMOutputParser.html#langchain_core.output_parsers.base.BaseLLMOutputParser "langchain_core.output_parsers.base.BaseLLMOutputParser")()Abstract base class for parsing the outputs of a model.
[`output_parsers.base.BaseOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.base.BaseOutputParser.html#langchain_core.output_parsers.base.BaseOutputParser "langchain_core.output_parsers.base.BaseOutputParser")Base class to parse the output of an LLM call.
[`output_parsers.json.JsonOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.json.JsonOutputParser.html#langchain_core.output_parsers.json.JsonOutputParser "langchain_core.output_parsers.json.JsonOutputParser")Parse the output of an LLM call to a JSON object.
[`output_parsers.json.SimpleJsonOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.json.SimpleJsonOutputParser.html#langchain_core.output_parsers.json.SimpleJsonOutputParser "langchain_core.output_parsers.json.SimpleJsonOutputParser")alias of [`JsonOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.json.JsonOutputParser.html#langchain_core.output_parsers.json.JsonOutputParser "langchain_core.output_parsers.json.JsonOutputParser")
[`output_parsers.list.CommaSeparatedListOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.list.CommaSeparatedListOutputParser.html#langchain_core.output_parsers.list.CommaSeparatedListOutputParser "langchain_core.output_parsers.list.CommaSeparatedListOutputParser")Parse the output of an LLM call to a comma-separated list.
[`output_parsers.list.ListOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.list.ListOutputParser.html#langchain_core.output_parsers.list.ListOutputParser "langchain_core.output_parsers.list.ListOutputParser")Parse the output of an LLM call to a list.
[`output_parsers.list.MarkdownListOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.list.MarkdownListOutputParser.html#langchain_core.output_parsers.list.MarkdownListOutputParser "langchain_core.output_parsers.list.MarkdownListOutputParser")Parse a Markdown list.
[`output_parsers.list.NumberedListOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.list.NumberedListOutputParser.html#langchain_core.output_parsers.list.NumberedListOutputParser "langchain_core.output_parsers.list.NumberedListOutputParser")Parse a numbered list.
[`output_parsers.openai_functions.JsonKeyOutputFunctionsParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_functions.JsonKeyOutputFunctionsParser.html#langchain_core.output_parsers.openai_functions.JsonKeyOutputFunctionsParser "langchain_core.output_parsers.openai_functions.JsonKeyOutputFunctionsParser")Parse an output as the element of the Json object.
[`output_parsers.openai_functions.JsonOutputFunctionsParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_functions.JsonOutputFunctionsParser.html#langchain_core.output_parsers.openai_functions.JsonOutputFunctionsParser "langchain_core.output_parsers.openai_functions.JsonOutputFunctionsParser")Parse an output as the Json object.
[`output_parsers.openai_functions.OutputFunctionsParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_functions.OutputFunctionsParser.html#langchain_core.output_parsers.openai_functions.OutputFunctionsParser "langchain_core.output_parsers.openai_functions.OutputFunctionsParser")Parse an output that is one of sets of values.
[`output_parsers.openai_functions.PydanticAttrOutputFunctionsParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_functions.PydanticAttrOutputFunctionsParser.html#langchain_core.output_parsers.openai_functions.PydanticAttrOutputFunctionsParser "langchain_core.output_parsers.openai_functions.PydanticAttrOutputFunctionsParser")Parse an output as an attribute of a pydantic object.
[`output_parsers.openai_functions.PydanticOutputFunctionsParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_functions.PydanticOutputFunctionsParser.html#langchain_core.output_parsers.openai_functions.PydanticOutputFunctionsParser "langchain_core.output_parsers.openai_functions.PydanticOutputFunctionsParser")Parse an output as a pydantic object.
[`output_parsers.openai_tools.JsonOutputKeyToolsParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_tools.JsonOutputKeyToolsParser.html#langchain_core.output_parsers.openai_tools.JsonOutputKeyToolsParser "langchain_core.output_parsers.openai_tools.JsonOutputKeyToolsParser")Parse tools from OpenAI response.
[`output_parsers.openai_tools.JsonOutputToolsParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_tools.JsonOutputToolsParser.html#langchain_core.output_parsers.openai_tools.JsonOutputToolsParser "langchain_core.output_parsers.openai_tools.JsonOutputToolsParser")Parse tools from OpenAI response.
[`output_parsers.openai_tools.PydanticToolsParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.openai_tools.PydanticToolsParser.html#langchain_core.output_parsers.openai_tools.PydanticToolsParser "langchain_core.output_parsers.openai_tools.PydanticToolsParser")Parse tools from OpenAI response.
[`output_parsers.pydantic.PydanticOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.pydantic.PydanticOutputParser.html#langchain_core.output_parsers.pydantic.PydanticOutputParser "langchain_core.output_parsers.pydantic.PydanticOutputParser")Parse an output using a pydantic model.
[`output_parsers.string.StrOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.string.StrOutputParser.html#langchain_core.output_parsers.string.StrOutputParser "langchain_core.output_parsers.string.StrOutputParser")OutputParser that parses LLMResult into the top likely string.
[`output_parsers.transform.BaseCumulativeTransformOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.transform.BaseCumulativeTransformOutputParser.html#langchain_core.output_parsers.transform.BaseCumulativeTransformOutputParser "langchain_core.output_parsers.transform.BaseCumulativeTransformOutputParser")Base class for an output parser that can handle streaming input.
`output_parsers.transform.BaseCumulativeTransformOutputParser[Any]`Base class for an output parser that can handle streaming input.
[`output_parsers.transform.BaseTransformOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.transform.BaseTransformOutputParser.html#langchain_core.output_parsers.transform.BaseTransformOutputParser "langchain_core.output_parsers.transform.BaseTransformOutputParser")Base class for an output parser that can handle streaming input.
`output_parsers.transform.BaseTransformOutputParser[list[str]]`Base class for an output parser that can handle streaming input.
`output_parsers.transform.BaseTransformOutputParser[str]`Base class for an output parser that can handle streaming input.
[`output_parsers.xml.XMLOutputParser`](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.xml.XMLOutputParser.html#langchain_core.output_parsers.xml.XMLOutputParser "langchain_core.output_parsers.xml.XMLOutputParser")Parse an output using xml format.

**Functions**

[outputs](https://python.langchain.com/api_reference/core/outputs.html#langchain-core-outputs)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-outputs "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`outputs.chat_generation.ChatGeneration`](https://python.langchain.com/api_reference/core/outputs/langchain_core.outputs.chat_generation.ChatGeneration.html#langchain_core.outputs.chat_generation.ChatGeneration "langchain_core.outputs.chat_generation.ChatGeneration")A single chat generation output.
[`outputs.chat_generation.ChatGenerationChunk`](https://python.langchain.com/api_reference/core/outputs/langchain_core.outputs.chat_generation.ChatGenerationChunk.html#langchain_core.outputs.chat_generation.ChatGenerationChunk "langchain_core.outputs.chat_generation.ChatGenerationChunk")ChatGeneration chunk.
[`outputs.chat_result.ChatResult`](https://python.langchain.com/api_reference/core/outputs/langchain_core.outputs.chat_result.ChatResult.html#langchain_core.outputs.chat_result.ChatResult "langchain_core.outputs.chat_result.ChatResult")Use to represent the result of a chat model call with a single prompt.
[`outputs.generation.Generation`](https://python.langchain.com/api_reference/core/outputs/langchain_core.outputs.generation.Generation.html#langchain_core.outputs.generation.Generation "langchain_core.outputs.generation.Generation")A single text generation output.
[`outputs.generation.GenerationChunk`](https://python.langchain.com/api_reference/core/outputs/langchain_core.outputs.generation.GenerationChunk.html#langchain_core.outputs.generation.GenerationChunk "langchain_core.outputs.generation.GenerationChunk")Generation chunk, which can be concatenated with other Generation chunks.
[`outputs.llm_result.LLMResult`](https://python.langchain.com/api_reference/core/outputs/langchain_core.outputs.llm_result.LLMResult.html#langchain_core.outputs.llm_result.LLMResult "langchain_core.outputs.llm_result.LLMResult")A container for results of an LLM call.
[`outputs.run_info.RunInfo`](https://python.langchain.com/api_reference/core/outputs/langchain_core.outputs.run_info.RunInfo.html#langchain_core.outputs.run_info.RunInfo "langchain_core.outputs.run_info.RunInfo")Class that contains metadata for a single execution of a Chain or model.

**Functions**

[prompt_values](https://python.langchain.com/api_reference/core/prompt_values.html#langchain-core-prompt-values)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-prompt-values "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`prompt_values.ChatPromptValue`](https://python.langchain.com/api_reference/core/prompt_values/langchain_core.prompt_values.ChatPromptValue.html#langchain_core.prompt_values.ChatPromptValue "langchain_core.prompt_values.ChatPromptValue")Chat prompt value.
[`prompt_values.ChatPromptValueConcrete`](https://python.langchain.com/api_reference/core/prompt_values/langchain_core.prompt_values.ChatPromptValueConcrete.html#langchain_core.prompt_values.ChatPromptValueConcrete "langchain_core.prompt_values.ChatPromptValueConcrete")Chat prompt value which explicitly lists out the message types it accepts.
[`prompt_values.ImagePromptValue`](https://python.langchain.com/api_reference/core/prompt_values/langchain_core.prompt_values.ImagePromptValue.html#langchain_core.prompt_values.ImagePromptValue "langchain_core.prompt_values.ImagePromptValue")Image prompt value.
[`prompt_values.ImageURL`](https://python.langchain.com/api_reference/core/prompt_values/langchain_core.prompt_values.ImageURL.html#langchain_core.prompt_values.ImageURL "langchain_core.prompt_values.ImageURL")Image URL.
[`prompt_values.PromptValue`](https://python.langchain.com/api_reference/core/prompt_values/langchain_core.prompt_values.PromptValue.html#langchain_core.prompt_values.PromptValue "langchain_core.prompt_values.PromptValue")Base abstract class for inputs to any language model.
[`prompt_values.StringPromptValue`](https://python.langchain.com/api_reference/core/prompt_values/langchain_core.prompt_values.StringPromptValue.html#langchain_core.prompt_values.StringPromptValue "langchain_core.prompt_values.StringPromptValue")String prompt value.

[prompts](https://python.langchain.com/api_reference/core/prompts.html#langchain-core-prompts)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-prompts "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`prompts.base.BasePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.base.BasePromptTemplate.html#langchain_core.prompts.base.BasePromptTemplate "langchain_core.prompts.base.BasePromptTemplate")Base class for all prompt templates, returning a prompt.
[`prompts.chat.AIMessagePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.AIMessagePromptTemplate.html#langchain_core.prompts.chat.AIMessagePromptTemplate "langchain_core.prompts.chat.AIMessagePromptTemplate")AI message prompt template.
[`prompts.chat.BaseChatPromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.BaseChatPromptTemplate.html#langchain_core.prompts.chat.BaseChatPromptTemplate "langchain_core.prompts.chat.BaseChatPromptTemplate")Base class for chat prompt templates.
[`prompts.chat.BaseStringMessagePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.BaseStringMessagePromptTemplate.html#langchain_core.prompts.chat.BaseStringMessagePromptTemplate "langchain_core.prompts.chat.BaseStringMessagePromptTemplate")Base class for message prompt templates that use a string prompt template.
[`prompts.chat.ChatMessagePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.ChatMessagePromptTemplate.html#langchain_core.prompts.chat.ChatMessagePromptTemplate "langchain_core.prompts.chat.ChatMessagePromptTemplate")Chat message prompt template.
[`prompts.chat.ChatPromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.ChatPromptTemplate.html#langchain_core.prompts.chat.ChatPromptTemplate "langchain_core.prompts.chat.ChatPromptTemplate")Prompt template for chat models.
[`prompts.chat.HumanMessagePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.HumanMessagePromptTemplate.html#langchain_core.prompts.chat.HumanMessagePromptTemplate "langchain_core.prompts.chat.HumanMessagePromptTemplate")Human message prompt template.
[`prompts.chat.MessagesPlaceholder`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.MessagesPlaceholder.html#langchain_core.prompts.chat.MessagesPlaceholder "langchain_core.prompts.chat.MessagesPlaceholder")Prompt template that assumes variable is already list of messages.
[`prompts.chat.SystemMessagePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.chat.SystemMessagePromptTemplate.html#langchain_core.prompts.chat.SystemMessagePromptTemplate "langchain_core.prompts.chat.SystemMessagePromptTemplate")System message prompt template.
[`prompts.dict.DictPromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.dict.DictPromptTemplate.html#langchain_core.prompts.dict.DictPromptTemplate "langchain_core.prompts.dict.DictPromptTemplate")Template represented by a dict.
[`prompts.few_shot.FewShotChatMessagePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.few_shot.FewShotChatMessagePromptTemplate.html#langchain_core.prompts.few_shot.FewShotChatMessagePromptTemplate "langchain_core.prompts.few_shot.FewShotChatMessagePromptTemplate")Chat prompt template that supports few-shot examples.
[`prompts.few_shot.FewShotPromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.few_shot.FewShotPromptTemplate.html#langchain_core.prompts.few_shot.FewShotPromptTemplate "langchain_core.prompts.few_shot.FewShotPromptTemplate")Prompt template that contains few shot examples.
[`prompts.few_shot_with_templates.FewShotPromptWithTemplates`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.few_shot_with_templates.FewShotPromptWithTemplates.html#langchain_core.prompts.few_shot_with_templates.FewShotPromptWithTemplates "langchain_core.prompts.few_shot_with_templates.FewShotPromptWithTemplates")Prompt template that contains few shot examples.
[`prompts.image.ImagePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.image.ImagePromptTemplate.html#langchain_core.prompts.image.ImagePromptTemplate "langchain_core.prompts.image.ImagePromptTemplate")Image prompt template for a multimodal model.
[`prompts.message.BaseMessagePromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.message.BaseMessagePromptTemplate.html#langchain_core.prompts.message.BaseMessagePromptTemplate "langchain_core.prompts.message.BaseMessagePromptTemplate")Base class for message prompt templates.
[`prompts.prompt.PromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.prompt.PromptTemplate.html#langchain_core.prompts.prompt.PromptTemplate "langchain_core.prompts.prompt.PromptTemplate")Prompt template for a language model.
[`prompts.string.StringPromptTemplate`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.string.StringPromptTemplate.html#langchain_core.prompts.string.StringPromptTemplate "langchain_core.prompts.string.StringPromptTemplate")String prompt that exposes the format method, returning a prompt.
[`prompts.structured.StructuredPrompt`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.structured.StructuredPrompt.html#langchain_core.prompts.structured.StructuredPrompt "langchain_core.prompts.structured.StructuredPrompt")

**Functions**

[`prompts.base.aformat_document`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.base.aformat_document.html#langchain_core.prompts.base.aformat_document "langchain_core.prompts.base.aformat_document")(doc,prompt)Async format a document into a string based on a prompt template.
[`prompts.base.format_document`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.base.format_document.html#langchain_core.prompts.base.format_document "langchain_core.prompts.base.format_document")(doc,prompt)Format a document into a string based on a prompt template.
[`prompts.loading.load_prompt`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.loading.load_prompt.html#langchain_core.prompts.loading.load_prompt "langchain_core.prompts.loading.load_prompt")(path[,encoding])Unified method for loading a prompt from LangChainHub or local fs.
[`prompts.loading.load_prompt_from_config`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.loading.load_prompt_from_config.html#langchain_core.prompts.loading.load_prompt_from_config "langchain_core.prompts.loading.load_prompt_from_config")(config)Load prompt from Config Dict.
[`prompts.string.check_valid_template`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.string.check_valid_template.html#langchain_core.prompts.string.check_valid_template "langchain_core.prompts.string.check_valid_template")(...)Check that template string is valid.
[`prompts.string.get_template_variables`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.string.get_template_variables.html#langchain_core.prompts.string.get_template_variables "langchain_core.prompts.string.get_template_variables")(...)Get the variables from the template.
[`prompts.string.jinja2_formatter`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.string.jinja2_formatter.html#langchain_core.prompts.string.jinja2_formatter "langchain_core.prompts.string.jinja2_formatter")(template,/,...)Format a template using jinja2.
[`prompts.string.mustache_formatter`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.string.mustache_formatter.html#langchain_core.prompts.string.mustache_formatter "langchain_core.prompts.string.mustache_formatter")(template,...)Format a template using mustache.
[`prompts.string.mustache_schema`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.string.mustache_schema.html#langchain_core.prompts.string.mustache_schema "langchain_core.prompts.string.mustache_schema")(template)Get the variables from a mustache template.
[`prompts.string.mustache_template_vars`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.string.mustache_template_vars.html#langchain_core.prompts.string.mustache_template_vars "langchain_core.prompts.string.mustache_template_vars")(template)Get the variables from a mustache template.
[`prompts.string.validate_jinja2`](https://python.langchain.com/api_reference/core/prompts/langchain_core.prompts.string.validate_jinja2.html#langchain_core.prompts.string.validate_jinja2 "langchain_core.prompts.string.validate_jinja2")(template,...)Validate that the input variables are valid for the template.

**Deprecated classes**

[rate_limiters](https://python.langchain.com/api_reference/core/rate_limiters.html#langchain-core-rate-limiters)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-rate-limiters "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[retrievers](https://python.langchain.com/api_reference/core/retrievers.html#langchain-core-retrievers)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-retrievers "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[runnables](https://python.langchain.com/api_reference/core/runnables.html#langchain-core-runnables)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-runnables "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`runnables.base.Runnable`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.Runnable.html#langchain_core.runnables.base.Runnable "langchain_core.runnables.base.Runnable")()A unit of work that can be invoked, batched, streamed, transformed and composed.
[`runnables.base.RunnableBinding`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableBinding.html#langchain_core.runnables.base.RunnableBinding "langchain_core.runnables.base.RunnableBinding")Wrap a Runnable with additional functionality.
[`runnables.base.RunnableBindingBase`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableBindingBase.html#langchain_core.runnables.base.RunnableBindingBase "langchain_core.runnables.base.RunnableBindingBase")Runnable that delegates calls to another Runnable with a set of kwargs.
[`runnables.base.RunnableEach`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableEach.html#langchain_core.runnables.base.RunnableEach "langchain_core.runnables.base.RunnableEach")Runnable that calls another Runnable for each element of the input sequence.
[`runnables.base.RunnableEachBase`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableEachBase.html#langchain_core.runnables.base.RunnableEachBase "langchain_core.runnables.base.RunnableEachBase")Runnable that calls another Runnable for each element of the input sequence.
[`runnables.base.RunnableGenerator`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableGenerator.html#langchain_core.runnables.base.RunnableGenerator "langchain_core.runnables.base.RunnableGenerator")(transform)Runnable that runs a generator function.
[`runnables.base.RunnableLambda`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableLambda.html#langchain_core.runnables.base.RunnableLambda "langchain_core.runnables.base.RunnableLambda")(func[,afunc,...])RunnableLambda converts a python callable into a Runnable.
[`runnables.base.RunnableMap`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableMap.html#langchain_core.runnables.base.RunnableMap "langchain_core.runnables.base.RunnableMap")alias of [`RunnableParallel`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableParallel.html#langchain_core.runnables.base.RunnableParallel "langchain_core.runnables.base.RunnableParallel")
[`runnables.base.RunnableParallel`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableParallel.html#langchain_core.runnables.base.RunnableParallel "langchain_core.runnables.base.RunnableParallel")Runnable that runs a mapping of Runnables in parallel.
[`runnables.base.RunnableSequence`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableSequence.html#langchain_core.runnables.base.RunnableSequence "langchain_core.runnables.base.RunnableSequence")Sequence of Runnables, where the output of each is the input of the next.
[`runnables.base.RunnableSerializable`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableSerializable.html#langchain_core.runnables.base.RunnableSerializable "langchain_core.runnables.base.RunnableSerializable")Runnable that can be serialized to JSON.
[`runnables.branch.RunnableBranch`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.branch.RunnableBranch.html#langchain_core.runnables.branch.RunnableBranch "langchain_core.runnables.branch.RunnableBranch")Runnable that selects which branch to run based on a condition.
[`runnables.config.ContextThreadPoolExecutor`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.ContextThreadPoolExecutor.html#langchain_core.runnables.config.ContextThreadPoolExecutor "langchain_core.runnables.config.ContextThreadPoolExecutor")([...])ThreadPoolExecutor that copies the context to the child thread.
[`runnables.config.EmptyDict`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.EmptyDict.html#langchain_core.runnables.config.EmptyDict "langchain_core.runnables.config.EmptyDict")Empty dict type.
[`runnables.config.RunnableConfig`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.RunnableConfig.html#langchain_core.runnables.config.RunnableConfig "langchain_core.runnables.config.RunnableConfig")Configuration for a Runnable.
[`runnables.configurable.DynamicRunnable`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.configurable.DynamicRunnable.html#langchain_core.runnables.configurable.DynamicRunnable "langchain_core.runnables.configurable.DynamicRunnable")Serializable Runnable that can be dynamically configured.
[`runnables.configurable.RunnableConfigurableAlternatives`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.configurable.RunnableConfigurableAlternatives.html#langchain_core.runnables.configurable.RunnableConfigurableAlternatives "langchain_core.runnables.configurable.RunnableConfigurableAlternatives")Runnable that can be dynamically configured.
[`runnables.configurable.RunnableConfigurableFields`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.configurable.RunnableConfigurableFields.html#langchain_core.runnables.configurable.RunnableConfigurableFields "langchain_core.runnables.configurable.RunnableConfigurableFields")Runnable that can be dynamically configured.
[`runnables.configurable.StrEnum`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.configurable.StrEnum.html#langchain_core.runnables.configurable.StrEnum "langchain_core.runnables.configurable.StrEnum")(value)String enum.
[`runnables.fallbacks.RunnableWithFallbacks`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.fallbacks.RunnableWithFallbacks.html#langchain_core.runnables.fallbacks.RunnableWithFallbacks "langchain_core.runnables.fallbacks.RunnableWithFallbacks")Runnable that can fallback to other Runnables if it fails.
[`runnables.graph.Branch`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.Branch.html#langchain_core.runnables.graph.Branch "langchain_core.runnables.graph.Branch")(condition,ends)Branch in a graph.
[`runnables.graph.CurveStyle`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.CurveStyle.html#langchain_core.runnables.graph.CurveStyle "langchain_core.runnables.graph.CurveStyle")(value)Enum for different curve styles supported by Mermaid.
[`runnables.graph.Edge`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.Edge.html#langchain_core.runnables.graph.Edge "langchain_core.runnables.graph.Edge")(source,target[,data,...])Edge in a graph.
[`runnables.graph.Graph`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.Graph.html#langchain_core.runnables.graph.Graph "langchain_core.runnables.graph.Graph")(nodes,...)Graph of nodes and edges.
[`runnables.graph.LabelsDict`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.LabelsDict.html#langchain_core.runnables.graph.LabelsDict "langchain_core.runnables.graph.LabelsDict")Dictionary of labels for nodes and edges in a graph.
[`runnables.graph.MermaidDrawMethod`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.MermaidDrawMethod.html#langchain_core.runnables.graph.MermaidDrawMethod "langchain_core.runnables.graph.MermaidDrawMethod")(value)Enum for different draw methods supported by Mermaid.
[`runnables.graph.Node`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.Node.html#langchain_core.runnables.graph.Node "langchain_core.runnables.graph.Node")(id,name,data,metadata)Node in a graph.
[`runnables.graph.NodeStyles`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.NodeStyles.html#langchain_core.runnables.graph.NodeStyles "langchain_core.runnables.graph.NodeStyles")([default,first,...])Schema for Hexadecimal color codes for different node types.
[`runnables.graph.Stringifiable`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.Stringifiable.html#langchain_core.runnables.graph.Stringifiable "langchain_core.runnables.graph.Stringifiable")(*args,**kwargs)Protocol for objects that can be converted to a string.
[`runnables.graph_ascii.AsciiCanvas`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph_ascii.AsciiCanvas.html#langchain_core.runnables.graph_ascii.AsciiCanvas "langchain_core.runnables.graph_ascii.AsciiCanvas")(cols,lines)Class for drawing in ASCII.
[`runnables.graph_ascii.VertexViewer`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph_ascii.VertexViewer.html#langchain_core.runnables.graph_ascii.VertexViewer "langchain_core.runnables.graph_ascii.VertexViewer")(name)VertexViewer class.
[`runnables.graph_png.PngDrawer`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph_png.PngDrawer.html#langchain_core.runnables.graph_png.PngDrawer "langchain_core.runnables.graph_png.PngDrawer")([fontname,labels])Helper class to draw a state graph into a PNG file.
[`runnables.history.RunnableWithMessageHistory`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.history.RunnableWithMessageHistory.html#langchain_core.runnables.history.RunnableWithMessageHistory "langchain_core.runnables.history.RunnableWithMessageHistory")Runnable that manages chat message history for another Runnable.
[`runnables.passthrough.RunnableAssign`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.passthrough.RunnableAssign.html#langchain_core.runnables.passthrough.RunnableAssign "langchain_core.runnables.passthrough.RunnableAssign")Runnable that assigns key-value pairs to dict[str, Any] inputs.
[`runnables.passthrough.RunnablePassthrough`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.passthrough.RunnablePassthrough.html#langchain_core.runnables.passthrough.RunnablePassthrough "langchain_core.runnables.passthrough.RunnablePassthrough")Runnable to passthrough inputs unchanged or with additional keys.
[`runnables.passthrough.RunnablePick`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.passthrough.RunnablePick.html#langchain_core.runnables.passthrough.RunnablePick "langchain_core.runnables.passthrough.RunnablePick")Runnable that picks keys from dict[str, Any] inputs.
[`runnables.retry.ExponentialJitterParams`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.retry.ExponentialJitterParams.html#langchain_core.runnables.retry.ExponentialJitterParams "langchain_core.runnables.retry.ExponentialJitterParams")Parameters for `tenacity.wait_exponential_jitter`.
[`runnables.retry.RunnableRetry`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.retry.RunnableRetry.html#langchain_core.runnables.retry.RunnableRetry "langchain_core.runnables.retry.RunnableRetry")Retry a Runnable if it fails.
[`runnables.router.RouterInput`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.router.RouterInput.html#langchain_core.runnables.router.RouterInput "langchain_core.runnables.router.RouterInput")Router input.
[`runnables.router.RouterRunnable`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.router.RouterRunnable.html#langchain_core.runnables.router.RouterRunnable "langchain_core.runnables.router.RouterRunnable")Runnable that routes to a set of Runnables based on Input['key'].
[`runnables.schema.BaseStreamEvent`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.schema.BaseStreamEvent.html#langchain_core.runnables.schema.BaseStreamEvent "langchain_core.runnables.schema.BaseStreamEvent")Streaming event.
[`runnables.schema.CustomStreamEvent`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.schema.CustomStreamEvent.html#langchain_core.runnables.schema.CustomStreamEvent "langchain_core.runnables.schema.CustomStreamEvent")Custom stream event created by the user.
[`runnables.schema.EventData`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.schema.EventData.html#langchain_core.runnables.schema.EventData "langchain_core.runnables.schema.EventData")Data associated with a streaming event.
[`runnables.schema.StandardStreamEvent`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.schema.StandardStreamEvent.html#langchain_core.runnables.schema.StandardStreamEvent "langchain_core.runnables.schema.StandardStreamEvent")A standard stream event that follows LangChain convention for event data.
[`runnables.utils.AddableDict`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.AddableDict.html#langchain_core.runnables.utils.AddableDict "langchain_core.runnables.utils.AddableDict")Dictionary that can be added to another dictionary.
[`runnables.utils.ConfigurableField`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.ConfigurableField.html#langchain_core.runnables.utils.ConfigurableField "langchain_core.runnables.utils.ConfigurableField")(id[,...])Field that can be configured by the user.
[`runnables.utils.ConfigurableFieldMultiOption`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.ConfigurableFieldMultiOption.html#langchain_core.runnables.utils.ConfigurableFieldMultiOption "langchain_core.runnables.utils.ConfigurableFieldMultiOption")(id,...)Field that can be configured by the user with multiple default values.
[`runnables.utils.ConfigurableFieldSingleOption`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.ConfigurableFieldSingleOption.html#langchain_core.runnables.utils.ConfigurableFieldSingleOption "langchain_core.runnables.utils.ConfigurableFieldSingleOption")(id,...)Field that can be configured by the user with a default value.
[`runnables.utils.ConfigurableFieldSpec`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.ConfigurableFieldSpec.html#langchain_core.runnables.utils.ConfigurableFieldSpec "langchain_core.runnables.utils.ConfigurableFieldSpec")(id,...)Field that can be configured by the user.
[`runnables.utils.FunctionNonLocals`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.FunctionNonLocals.html#langchain_core.runnables.utils.FunctionNonLocals "langchain_core.runnables.utils.FunctionNonLocals")()Get the nonlocal variables accessed of a function.
[`runnables.utils.GetLambdaSource`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.GetLambdaSource.html#langchain_core.runnables.utils.GetLambdaSource "langchain_core.runnables.utils.GetLambdaSource")()Get the source code of a lambda function.
[`runnables.utils.IsFunctionArgDict`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.IsFunctionArgDict.html#langchain_core.runnables.utils.IsFunctionArgDict "langchain_core.runnables.utils.IsFunctionArgDict")()Check if the first argument of a function is a dict.
[`runnables.utils.IsLocalDict`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.IsLocalDict.html#langchain_core.runnables.utils.IsLocalDict "langchain_core.runnables.utils.IsLocalDict")(name,keys)Check if a name is a local dict.
[`runnables.utils.NonLocals`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.NonLocals.html#langchain_core.runnables.utils.NonLocals "langchain_core.runnables.utils.NonLocals")()Get nonlocal variables accessed.
[`runnables.utils.SupportsAdd`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.SupportsAdd.html#langchain_core.runnables.utils.SupportsAdd "langchain_core.runnables.utils.SupportsAdd")(*args,**kwargs)Protocol for objects that support addition.

**Functions**

[`runnables.base.chain`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.chain.html#langchain_core.runnables.base.chain "langchain_core.runnables.base.chain")()Decorate a function to make it a Runnable.
[`runnables.base.coerce_to_runnable`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.coerce_to_runnable.html#langchain_core.runnables.base.coerce_to_runnable "langchain_core.runnables.base.coerce_to_runnable")(thing)Coerce a Runnable-like object into a Runnable.
[`runnables.config.acall_func_with_variable_args`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.acall_func_with_variable_args.html#langchain_core.runnables.config.acall_func_with_variable_args "langchain_core.runnables.config.acall_func_with_variable_args")(...)Async call function that may optionally accept a run_manager and/or config.
[`runnables.config.call_func_with_variable_args`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.call_func_with_variable_args.html#langchain_core.runnables.config.call_func_with_variable_args "langchain_core.runnables.config.call_func_with_variable_args")(...)Call function that may optionally accept a run_manager and/or config.
[`runnables.config.ensure_config`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.ensure_config.html#langchain_core.runnables.config.ensure_config "langchain_core.runnables.config.ensure_config")([config])Ensure that a config is a dict with all keys present.
[`runnables.config.get_async_callback_manager_for_config`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.get_async_callback_manager_for_config.html#langchain_core.runnables.config.get_async_callback_manager_for_config "langchain_core.runnables.config.get_async_callback_manager_for_config")(config)Get an async callback manager for a config.
[`runnables.config.get_callback_manager_for_config`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.get_callback_manager_for_config.html#langchain_core.runnables.config.get_callback_manager_for_config "langchain_core.runnables.config.get_callback_manager_for_config")(config)Get a callback manager for a config.
[`runnables.config.get_config_list`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.get_config_list.html#langchain_core.runnables.config.get_config_list "langchain_core.runnables.config.get_config_list")(config,length)Get a list of configs from a single config or a list of configs.
[`runnables.config.get_executor_for_config`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.get_executor_for_config.html#langchain_core.runnables.config.get_executor_for_config "langchain_core.runnables.config.get_executor_for_config")(config)Get an executor for a config.
[`runnables.config.merge_configs`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.merge_configs.html#langchain_core.runnables.config.merge_configs "langchain_core.runnables.config.merge_configs")(*configs)Merge multiple configs into one.
[`runnables.config.patch_config`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.patch_config.html#langchain_core.runnables.config.patch_config "langchain_core.runnables.config.patch_config")(config,*[,...])Patch a config with new values.
[`runnables.config.run_in_executor`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.run_in_executor.html#langchain_core.runnables.config.run_in_executor "langchain_core.runnables.config.run_in_executor")(...)Run a function in an executor.
[`runnables.config.set_config_context`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.set_config_context.html#langchain_core.runnables.config.set_config_context "langchain_core.runnables.config.set_config_context")(config)Set the child Runnable config + tracing context.
[`runnables.configurable.make_options_spec`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.configurable.make_options_spec.html#langchain_core.runnables.configurable.make_options_spec "langchain_core.runnables.configurable.make_options_spec")(...)Make a ConfigurableFieldSpec for a ConfigurableFieldSingleOption or ConfigurableFieldMultiOption.
[`runnables.configurable.prefix_config_spec`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.configurable.prefix_config_spec.html#langchain_core.runnables.configurable.prefix_config_spec "langchain_core.runnables.configurable.prefix_config_spec")(...)Prefix the id of a ConfigurableFieldSpec.
[`runnables.graph.is_uuid`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.is_uuid.html#langchain_core.runnables.graph.is_uuid "langchain_core.runnables.graph.is_uuid")(value)Check if a string is a valid UUID.
[`runnables.graph.node_data_json`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.node_data_json.html#langchain_core.runnables.graph.node_data_json "langchain_core.runnables.graph.node_data_json")(node,*[,...])Convert the data of a node to a JSON-serializable format.
[`runnables.graph.node_data_str`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph.node_data_str.html#langchain_core.runnables.graph.node_data_str "langchain_core.runnables.graph.node_data_str")(id,data)Convert the data of a node to a string.
[`runnables.graph_ascii.draw_ascii`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph_ascii.draw_ascii.html#langchain_core.runnables.graph_ascii.draw_ascii "langchain_core.runnables.graph_ascii.draw_ascii")(vertices,edges)Build a DAG and draw it in ASCII.
[`runnables.graph_mermaid.draw_mermaid`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph_mermaid.draw_mermaid.html#langchain_core.runnables.graph_mermaid.draw_mermaid "langchain_core.runnables.graph_mermaid.draw_mermaid")(nodes,...)Draws a Mermaid graph using the provided graph data.
[`runnables.graph_mermaid.draw_mermaid_png`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.graph_mermaid.draw_mermaid_png.html#langchain_core.runnables.graph_mermaid.draw_mermaid_png "langchain_core.runnables.graph_mermaid.draw_mermaid_png")(...)Draws a Mermaid graph as PNG using provided syntax.
[`runnables.passthrough.aidentity`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.passthrough.aidentity.html#langchain_core.runnables.passthrough.aidentity "langchain_core.runnables.passthrough.aidentity")(x)Async identity function.
[`runnables.passthrough.identity`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.passthrough.identity.html#langchain_core.runnables.passthrough.identity "langchain_core.runnables.passthrough.identity")(x)Identity function.
[`runnables.utils.aadd`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.aadd.html#langchain_core.runnables.utils.aadd "langchain_core.runnables.utils.aadd")(addables)Asynchronously add a sequence of addable objects together.
[`runnables.utils.accepts_config`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.accepts_config.html#langchain_core.runnables.utils.accepts_config "langchain_core.runnables.utils.accepts_config")(callable)Check if a callable accepts a config argument.
[`runnables.utils.accepts_context`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.accepts_context.html#langchain_core.runnables.utils.accepts_context "langchain_core.runnables.utils.accepts_context")(callable)Check if a callable accepts a context argument.
[`runnables.utils.accepts_run_manager`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.accepts_run_manager.html#langchain_core.runnables.utils.accepts_run_manager "langchain_core.runnables.utils.accepts_run_manager")(callable)Check if a callable accepts a run_manager argument.
[`runnables.utils.add`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.add.html#langchain_core.runnables.utils.add "langchain_core.runnables.utils.add")(addables)Add a sequence of addable objects together.
[`runnables.utils.coro_with_context`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.coro_with_context.html#langchain_core.runnables.utils.coro_with_context "langchain_core.runnables.utils.coro_with_context")(coro,...)Await a coroutine with a context.
[`runnables.utils.gated_coro`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.gated_coro.html#langchain_core.runnables.utils.gated_coro "langchain_core.runnables.utils.gated_coro")(semaphore,coro)Run a coroutine with a semaphore.
[`runnables.utils.gather_with_concurrency`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.gather_with_concurrency.html#langchain_core.runnables.utils.gather_with_concurrency "langchain_core.runnables.utils.gather_with_concurrency")(n,...)Gather coroutines with a limit on the number of concurrent coroutines.
[`runnables.utils.get_function_first_arg_dict_keys`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.get_function_first_arg_dict_keys.html#langchain_core.runnables.utils.get_function_first_arg_dict_keys "langchain_core.runnables.utils.get_function_first_arg_dict_keys")(func)Get the keys of the first argument of a function if it is a dict.
[`runnables.utils.get_lambda_source`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.get_lambda_source.html#langchain_core.runnables.utils.get_lambda_source "langchain_core.runnables.utils.get_lambda_source")(func)Get the source code of a lambda function.
[`runnables.utils.get_unique_config_specs`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.get_unique_config_specs.html#langchain_core.runnables.utils.get_unique_config_specs "langchain_core.runnables.utils.get_unique_config_specs")(specs)Get the unique config specs from a sequence of config specs.
[`runnables.utils.indent_lines_after_first`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.indent_lines_after_first.html#langchain_core.runnables.utils.indent_lines_after_first "langchain_core.runnables.utils.indent_lines_after_first")(...)Indent all lines of text after the first line.
[`runnables.utils.is_async_callable`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.is_async_callable.html#langchain_core.runnables.utils.is_async_callable "langchain_core.runnables.utils.is_async_callable")(func)Check if a function is async.
[`runnables.utils.is_async_generator`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.utils.is_async_generator.html#langchain_core.runnables.utils.is_async_generator "langchain_core.runnables.utils.is_async_generator")(func)Check if a function is an async generator.

[stores](https://python.langchain.com/api_reference/core/stores.html#langchain-core-stores)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-stores "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[structured_query](https://python.langchain.com/api_reference/core/structured_query.html#langchain-core-structured-query)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-structured-query "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`structured_query.Comparator`](https://python.langchain.com/api_reference/core/structured_query/langchain_core.structured_query.Comparator.html#langchain_core.structured_query.Comparator "langchain_core.structured_query.Comparator")(value)Enumerator of the comparison operators.
[`structured_query.Comparison`](https://python.langchain.com/api_reference/core/structured_query/langchain_core.structured_query.Comparison.html#langchain_core.structured_query.Comparison "langchain_core.structured_query.Comparison")Comparison to a value.
[`structured_query.Expr`](https://python.langchain.com/api_reference/core/structured_query/langchain_core.structured_query.Expr.html#langchain_core.structured_query.Expr "langchain_core.structured_query.Expr")Base class for all expressions.
[`structured_query.FilterDirective`](https://python.langchain.com/api_reference/core/structured_query/langchain_core.structured_query.FilterDirective.html#langchain_core.structured_query.FilterDirective "langchain_core.structured_query.FilterDirective")Filtering expression.
[`structured_query.Operation`](https://python.langchain.com/api_reference/core/structured_query/langchain_core.structured_query.Operation.html#langchain_core.structured_query.Operation "langchain_core.structured_query.Operation")Logical operation over other directives.
[`structured_query.Operator`](https://python.langchain.com/api_reference/core/structured_query/langchain_core.structured_query.Operator.html#langchain_core.structured_query.Operator "langchain_core.structured_query.Operator")(value)Enumerator of the operations.
[`structured_query.StructuredQuery`](https://python.langchain.com/api_reference/core/structured_query/langchain_core.structured_query.StructuredQuery.html#langchain_core.structured_query.StructuredQuery "langchain_core.structured_query.StructuredQuery")Structured query.
[`structured_query.Visitor`](https://python.langchain.com/api_reference/core/structured_query/langchain_core.structured_query.Visitor.html#langchain_core.structured_query.Visitor "langchain_core.structured_query.Visitor")()Defines interface for IR translation using a visitor pattern.

[sys_info](https://python.langchain.com/api_reference/core/sys_info.html#langchain-core-sys-info)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-sys-info "Link to this heading")
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Functions**

[tools](https://python.langchain.com/api_reference/core/tools.html#langchain-core-tools)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-tools "Link to this heading")
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`tools.base.BaseTool`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.BaseTool.html#langchain_core.tools.base.BaseTool "langchain_core.tools.base.BaseTool")Interface LangChain tools must implement.
[`tools.base.BaseToolkit`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.BaseToolkit.html#langchain_core.tools.base.BaseToolkit "langchain_core.tools.base.BaseToolkit")Base Toolkit representing a collection of related tools.
[`tools.base.InjectedToolArg`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.InjectedToolArg.html#langchain_core.tools.base.InjectedToolArg "langchain_core.tools.base.InjectedToolArg")()Annotation for a Tool arg that is **not** meant to be generated by a model.
[`tools.base.InjectedToolCallId`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.InjectedToolCallId.html#langchain_core.tools.base.InjectedToolCallId "langchain_core.tools.base.InjectedToolCallId")()Annotation for injecting the tool_call_id.
[`tools.base.SchemaAnnotationError`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.SchemaAnnotationError.html#langchain_core.tools.base.SchemaAnnotationError "langchain_core.tools.base.SchemaAnnotationError")Raised when 'args_schema' is missing or has an incorrect type annotation.
[`tools.base.ToolException`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.ToolException.html#langchain_core.tools.base.ToolException "langchain_core.tools.base.ToolException")Optional exception that tool throws when execution error occurs.
[`tools.retriever.RetrieverInput`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.retriever.RetrieverInput.html#langchain_core.tools.retriever.RetrieverInput "langchain_core.tools.retriever.RetrieverInput")Input to the retriever.
[`tools.simple.Tool`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.simple.Tool.html#langchain_core.tools.simple.Tool "langchain_core.tools.simple.Tool")Tool that takes in function or coroutine directly.
[`tools.structured.StructuredTool`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.structured.StructuredTool.html#langchain_core.tools.structured.StructuredTool "langchain_core.tools.structured.StructuredTool")Tool that can operate on any number of inputs.

**Functions**

[`tools.base.create_schema_from_function`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.create_schema_from_function.html#langchain_core.tools.base.create_schema_from_function "langchain_core.tools.base.create_schema_from_function")(...)Create a pydantic schema from a function's signature.
[`tools.base.get_all_basemodel_annotations`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.base.get_all_basemodel_annotations.html#langchain_core.tools.base.get_all_basemodel_annotations "langchain_core.tools.base.get_all_basemodel_annotations")(cls,*)Get all annotations from a Pydantic BaseModel and its parents.
[`tools.convert.convert_runnable_to_tool`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.convert.convert_runnable_to_tool.html#langchain_core.tools.convert.convert_runnable_to_tool "langchain_core.tools.convert.convert_runnable_to_tool")(runnable)Convert a Runnable into a BaseTool.
[`tools.convert.tool`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.convert.tool.html#langchain_core.tools.convert.tool "langchain_core.tools.convert.tool")()Make tools out of functions, can be used with or without arguments.
[`tools.render.render_text_description`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.render.render_text_description.html#langchain_core.tools.render.render_text_description "langchain_core.tools.render.render_text_description")(tools)Render the tool name and description in plain text.
[`tools.render.render_text_description_and_args`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.render.render_text_description_and_args.html#langchain_core.tools.render.render_text_description_and_args "langchain_core.tools.render.render_text_description_and_args")(tools)Render the tool name, description, and args in plain text.
[`tools.retriever.create_retriever_tool`](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.retriever.create_retriever_tool.html#langchain_core.tools.retriever.create_retriever_tool "langchain_core.tools.retriever.create_retriever_tool")(...[,...])Create a tool to do retrieval of documents.

[tracers](https://python.langchain.com/api_reference/core/tracers.html#langchain-core-tracers)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-tracers "Link to this heading")
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`tracers.base.AsyncBaseTracer`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.base.AsyncBaseTracer.html#langchain_core.tracers.base.AsyncBaseTracer "langchain_core.tracers.base.AsyncBaseTracer")(*[,_schema_format])Async Base interface for tracers.
[`tracers.base.BaseTracer`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.base.BaseTracer.html#langchain_core.tracers.base.BaseTracer "langchain_core.tracers.base.BaseTracer")(*[,_schema_format])Base interface for tracers.
[`tracers.evaluation.EvaluatorCallbackHandler`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.evaluation.EvaluatorCallbackHandler.html#langchain_core.tracers.evaluation.EvaluatorCallbackHandler "langchain_core.tracers.evaluation.EvaluatorCallbackHandler")(...)Tracer that runs a run evaluator whenever a run is persisted.
[`tracers.event_stream.RunInfo`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.event_stream.RunInfo.html#langchain_core.tracers.event_stream.RunInfo "langchain_core.tracers.event_stream.RunInfo")Information about a run.
[`tracers.langchain.LangChainTracer`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.langchain.LangChainTracer.html#langchain_core.tracers.langchain.LangChainTracer "langchain_core.tracers.langchain.LangChainTracer")([...])Implementation of the SharedTracer that POSTS to the LangChain endpoint.
[`tracers.log_stream.LogEntry`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.log_stream.LogEntry.html#langchain_core.tracers.log_stream.LogEntry "langchain_core.tracers.log_stream.LogEntry")A single entry in the run log.
[`tracers.log_stream.LogStreamCallbackHandler`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.log_stream.LogStreamCallbackHandler.html#langchain_core.tracers.log_stream.LogStreamCallbackHandler "langchain_core.tracers.log_stream.LogStreamCallbackHandler")(*)Tracer that streams run logs to a stream.
[`tracers.log_stream.RunLog`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.log_stream.RunLog.html#langchain_core.tracers.log_stream.RunLog "langchain_core.tracers.log_stream.RunLog")(*ops,state)Run log.
[`tracers.log_stream.RunLogPatch`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.log_stream.RunLogPatch.html#langchain_core.tracers.log_stream.RunLogPatch "langchain_core.tracers.log_stream.RunLogPatch")(*ops)Patch to the run log.
[`tracers.log_stream.RunState`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.log_stream.RunState.html#langchain_core.tracers.log_stream.RunState "langchain_core.tracers.log_stream.RunState")State of the run.
[`tracers.root_listeners.AsyncRootListenersTracer`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.root_listeners.AsyncRootListenersTracer.html#langchain_core.tracers.root_listeners.AsyncRootListenersTracer "langchain_core.tracers.root_listeners.AsyncRootListenersTracer")(*,...)Async Tracer that calls listeners on run start, end, and error.
[`tracers.root_listeners.RootListenersTracer`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.root_listeners.RootListenersTracer.html#langchain_core.tracers.root_listeners.RootListenersTracer "langchain_core.tracers.root_listeners.RootListenersTracer")(*,...)Tracer that calls listeners on run start, end, and error.
[`tracers.run_collector.RunCollectorCallbackHandler`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.run_collector.RunCollectorCallbackHandler.html#langchain_core.tracers.run_collector.RunCollectorCallbackHandler "langchain_core.tracers.run_collector.RunCollectorCallbackHandler")([...])Tracer that collects all nested runs in a list.
[`tracers.stdout.ConsoleCallbackHandler`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.stdout.ConsoleCallbackHandler.html#langchain_core.tracers.stdout.ConsoleCallbackHandler "langchain_core.tracers.stdout.ConsoleCallbackHandler")(**kwargs)Tracer that prints to the console.
[`tracers.stdout.FunctionCallbackHandler`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.stdout.FunctionCallbackHandler.html#langchain_core.tracers.stdout.FunctionCallbackHandler "langchain_core.tracers.stdout.FunctionCallbackHandler")(...)Tracer that calls a function with a single str parameter.

**Functions**

[`tracers.context.collect_runs`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.context.collect_runs.html#langchain_core.tracers.context.collect_runs "langchain_core.tracers.context.collect_runs")()Collect all run traces in context.
[`tracers.context.register_configure_hook`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.context.register_configure_hook.html#langchain_core.tracers.context.register_configure_hook "langchain_core.tracers.context.register_configure_hook")(...)Register a configure hook.
[`tracers.context.tracing_enabled`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.context.tracing_enabled.html#langchain_core.tracers.context.tracing_enabled "langchain_core.tracers.context.tracing_enabled")([session_name])Throw an error because this has been replaced by tracing_v2_enabled.
[`tracers.context.tracing_v2_enabled`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.context.tracing_v2_enabled.html#langchain_core.tracers.context.tracing_v2_enabled "langchain_core.tracers.context.tracing_v2_enabled")([...])Instruct LangChain to log all runs in context to LangSmith.
[`tracers.evaluation.wait_for_all_evaluators`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.evaluation.wait_for_all_evaluators.html#langchain_core.tracers.evaluation.wait_for_all_evaluators "langchain_core.tracers.evaluation.wait_for_all_evaluators")()Wait for all tracers to finish.
[`tracers.langchain.get_client`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.langchain.get_client.html#langchain_core.tracers.langchain.get_client "langchain_core.tracers.langchain.get_client")()Get the client.
[`tracers.langchain.log_error_once`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.langchain.log_error_once.html#langchain_core.tracers.langchain.log_error_once "langchain_core.tracers.langchain.log_error_once")(method,...)Log an error once.
[`tracers.langchain.wait_for_all_tracers`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.langchain.wait_for_all_tracers.html#langchain_core.tracers.langchain.wait_for_all_tracers "langchain_core.tracers.langchain.wait_for_all_tracers")()Wait for all tracers to finish.
[`tracers.langchain_v1.LangChainTracerV1`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.langchain_v1.LangChainTracerV1.html#langchain_core.tracers.langchain_v1.LangChainTracerV1 "langchain_core.tracers.langchain_v1.LangChainTracerV1")(...)Throw an error because this has been replaced by LangChainTracer.
[`tracers.langchain_v1.get_headers`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.langchain_v1.get_headers.html#langchain_core.tracers.langchain_v1.get_headers "langchain_core.tracers.langchain_v1.get_headers")(*args,**kwargs)Throw an error because this has been replaced by get_headers.
[`tracers.stdout.elapsed`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.stdout.elapsed.html#langchain_core.tracers.stdout.elapsed "langchain_core.tracers.stdout.elapsed")(run)Get the elapsed time of a run.
[`tracers.stdout.try_json_stringify`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.stdout.try_json_stringify.html#langchain_core.tracers.stdout.try_json_stringify "langchain_core.tracers.stdout.try_json_stringify")(obj,fallback)Try to stringify an object to JSON.

**Deprecated classes**

[`tracers.schemas.BaseRun`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.BaseRun.html#langchain_core.tracers.schemas.BaseRun "langchain_core.tracers.schemas.BaseRun")(*,uuid[,...])
[`tracers.schemas.ChainRun`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.ChainRun.html#langchain_core.tracers.schemas.ChainRun "langchain_core.tracers.schemas.ChainRun")(*,uuid[,...])
[`tracers.schemas.LLMRun`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.LLMRun.html#langchain_core.tracers.schemas.LLMRun "langchain_core.tracers.schemas.LLMRun")(*,uuid[,...])
[`tracers.schemas.ToolRun`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.ToolRun.html#langchain_core.tracers.schemas.ToolRun "langchain_core.tracers.schemas.ToolRun")(*,uuid[,...])
[`tracers.schemas.TracerSession`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.TracerSession.html#langchain_core.tracers.schemas.TracerSession "langchain_core.tracers.schemas.TracerSession")(*[,...])
[`tracers.schemas.TracerSessionBase`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.TracerSessionBase.html#langchain_core.tracers.schemas.TracerSessionBase "langchain_core.tracers.schemas.TracerSessionBase")(*[,...])
[`tracers.schemas.TracerSessionV1`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.TracerSessionV1.html#langchain_core.tracers.schemas.TracerSessionV1 "langchain_core.tracers.schemas.TracerSessionV1")(*[,...])
[`tracers.schemas.TracerSessionV1Base`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.TracerSessionV1Base.html#langchain_core.tracers.schemas.TracerSessionV1Base "langchain_core.tracers.schemas.TracerSessionV1Base")(*[,...])
[`tracers.schemas.TracerSessionV1Create`](https://python.langchain.com/api_reference/core/tracers/langchain_core.tracers.schemas.TracerSessionV1Create.html#langchain_core.tracers.schemas.TracerSessionV1Create "langchain_core.tracers.schemas.TracerSessionV1Create")(*[,...])

**Deprecated functions**

[utils](https://python.langchain.com/api_reference/core/utils.html#langchain-core-utils)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-utils "Link to this heading")
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

[`utils.aiter.NoLock`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.aiter.NoLock.html#langchain_core.utils.aiter.NoLock "langchain_core.utils.aiter.NoLock")()Dummy lock that provides the proper interface but no protection.
[`utils.aiter.Tee`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.aiter.Tee.html#langchain_core.utils.aiter.Tee "langchain_core.utils.aiter.Tee")(iterable[,n,lock])Create `n` separate asynchronous iterators over `iterable`.
[`utils.aiter.aclosing`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.aiter.aclosing.html#langchain_core.utils.aiter.aclosing "langchain_core.utils.aiter.aclosing")(thing)Async context manager to wrap an AsyncGenerator that has a `aclose()` method.
[`utils.aiter.atee`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.aiter.atee.html#langchain_core.utils.aiter.atee "langchain_core.utils.aiter.atee")alias of [`Tee`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.aiter.Tee.html#langchain_core.utils.aiter.Tee "langchain_core.utils.aiter.Tee")
[`utils.formatting.StrictFormatter`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.formatting.StrictFormatter.html#langchain_core.utils.formatting.StrictFormatter "langchain_core.utils.formatting.StrictFormatter")()Formatter that checks for extra keys.
[`utils.function_calling.FunctionDescription`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.FunctionDescription.html#langchain_core.utils.function_calling.FunctionDescription "langchain_core.utils.function_calling.FunctionDescription")Representation of a callable function to send to an LLM.
[`utils.function_calling.ToolDescription`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.ToolDescription.html#langchain_core.utils.function_calling.ToolDescription "langchain_core.utils.function_calling.ToolDescription")Representation of a callable function to the OpenAI API.
[`utils.iter.NoLock`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.iter.NoLock.html#langchain_core.utils.iter.NoLock "langchain_core.utils.iter.NoLock")()Dummy lock that provides the proper interface but no protection.
[`utils.iter.Tee`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.iter.Tee.html#langchain_core.utils.iter.Tee "langchain_core.utils.iter.Tee")(iterable[,n,lock])Create `n` separate asynchronous iterators over `iterable`.
[`utils.iter.safetee`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.iter.safetee.html#langchain_core.utils.iter.safetee "langchain_core.utils.iter.safetee")alias of [`Tee`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.iter.Tee.html#langchain_core.utils.iter.Tee "langchain_core.utils.iter.Tee")
[`utils.mustache.ChevronError`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.mustache.ChevronError.html#langchain_core.utils.mustache.ChevronError "langchain_core.utils.mustache.ChevronError")Custom exception for Chevron errors.

**Functions**

[`utils.aiter.abatch_iterate`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.aiter.abatch_iterate.html#langchain_core.utils.aiter.abatch_iterate "langchain_core.utils.aiter.abatch_iterate")(size,iterable)Utility batching function for async iterables.
[`utils.aiter.py_anext`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.aiter.py_anext.html#langchain_core.utils.aiter.py_anext "langchain_core.utils.aiter.py_anext")(iterator[,default])Pure-Python implementation of anext() for testing purposes.
[`utils.aiter.tee_peer`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.aiter.tee_peer.html#langchain_core.utils.aiter.tee_peer "langchain_core.utils.aiter.tee_peer")(iterator,buffer,...)An individual iterator of a `tee()`.
[`utils.env.env_var_is_set`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.env.env_var_is_set.html#langchain_core.utils.env.env_var_is_set "langchain_core.utils.env.env_var_is_set")(env_var)Check if an environment variable is set.
[`utils.env.get_from_dict_or_env`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.env.get_from_dict_or_env.html#langchain_core.utils.env.get_from_dict_or_env "langchain_core.utils.env.get_from_dict_or_env")(data,key,...)Get a value from a dictionary or an environment variable.
[`utils.env.get_from_env`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.env.get_from_env.html#langchain_core.utils.env.get_from_env "langchain_core.utils.env.get_from_env")(key,env_key[,default])Get a value from a dictionary or an environment variable.
[`utils.function_calling.convert_to_json_schema`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.convert_to_json_schema.html#langchain_core.utils.function_calling.convert_to_json_schema "langchain_core.utils.function_calling.convert_to_json_schema")(...)Convert a schema representation to a JSON schema.
[`utils.function_calling.convert_to_openai_function`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.convert_to_openai_function.html#langchain_core.utils.function_calling.convert_to_openai_function "langchain_core.utils.function_calling.convert_to_openai_function")(...)Convert a raw function/class to an OpenAI function.
[`utils.function_calling.convert_to_openai_tool`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.convert_to_openai_tool.html#langchain_core.utils.function_calling.convert_to_openai_tool "langchain_core.utils.function_calling.convert_to_openai_tool")(tool,*)Convert a tool-like object to an OpenAI tool schema.
[`utils.function_calling.tool_example_to_messages`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.tool_example_to_messages.html#langchain_core.utils.function_calling.tool_example_to_messages "langchain_core.utils.function_calling.tool_example_to_messages")(...)
[`utils.html.extract_sub_links`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.html.extract_sub_links.html#langchain_core.utils.html.extract_sub_links "langchain_core.utils.html.extract_sub_links")(raw_html,url,*)Extract all links from a raw HTML string and convert into absolute paths.
[`utils.html.find_all_links`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.html.find_all_links.html#langchain_core.utils.html.find_all_links "langchain_core.utils.html.find_all_links")(raw_html,*[,pattern])Extract all links from a raw HTML string.
[`utils.input.get_bolded_text`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.input.get_bolded_text.html#langchain_core.utils.input.get_bolded_text "langchain_core.utils.input.get_bolded_text")(text)Get bolded text.
[`utils.input.get_color_mapping`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.input.get_color_mapping.html#langchain_core.utils.input.get_color_mapping "langchain_core.utils.input.get_color_mapping")(items[,...])Get mapping for items to a support color.
[`utils.input.get_colored_text`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.input.get_colored_text.html#langchain_core.utils.input.get_colored_text "langchain_core.utils.input.get_colored_text")(text,color)Get colored text.
[`utils.input.print_text`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.input.print_text.html#langchain_core.utils.input.print_text "langchain_core.utils.input.print_text")(text[,color,end,file])Print text with highlighting and no end characters.
[`utils.interactive_env.is_interactive_env`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.interactive_env.is_interactive_env.html#langchain_core.utils.interactive_env.is_interactive_env "langchain_core.utils.interactive_env.is_interactive_env")()Determine if running within IPython or Jupyter.
[`utils.iter.batch_iterate`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.iter.batch_iterate.html#langchain_core.utils.iter.batch_iterate "langchain_core.utils.iter.batch_iterate")(size,iterable)Utility batching function.
[`utils.iter.tee_peer`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.iter.tee_peer.html#langchain_core.utils.iter.tee_peer "langchain_core.utils.iter.tee_peer")(iterator,buffer,peers,...)An individual iterator of a `tee()`.
[`utils.json.parse_and_check_json_markdown`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.json.parse_and_check_json_markdown.html#langchain_core.utils.json.parse_and_check_json_markdown "langchain_core.utils.json.parse_and_check_json_markdown")(...)Parse and check a JSON string from a Markdown string.
[`utils.json.parse_json_markdown`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.json.parse_json_markdown.html#langchain_core.utils.json.parse_json_markdown "langchain_core.utils.json.parse_json_markdown")(json_string,*)Parse a JSON string from a Markdown string.
[`utils.json.parse_partial_json`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.json.parse_partial_json.html#langchain_core.utils.json.parse_partial_json "langchain_core.utils.json.parse_partial_json")(s,*[,strict])Parse a JSON string that may be missing closing braces.
[`utils.json_schema.dereference_refs`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.json_schema.dereference_refs.html#langchain_core.utils.json_schema.dereference_refs "langchain_core.utils.json_schema.dereference_refs")(schema_obj,*)Try to substitute $refs in JSON Schema.
[`utils.mustache.grab_literal`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.mustache.grab_literal.html#langchain_core.utils.mustache.grab_literal "langchain_core.utils.mustache.grab_literal")(template,l_del)Parse a literal from the template.
[`utils.mustache.l_sa_check`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.mustache.l_sa_check.html#langchain_core.utils.mustache.l_sa_check "langchain_core.utils.mustache.l_sa_check")(template,literal,...)Do a preliminary check to see if a tag could be a standalone.
[`utils.mustache.parse_tag`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.mustache.parse_tag.html#langchain_core.utils.mustache.parse_tag "langchain_core.utils.mustache.parse_tag")(template,l_del,r_del)Parse a tag from a template.
[`utils.mustache.r_sa_check`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.mustache.r_sa_check.html#langchain_core.utils.mustache.r_sa_check "langchain_core.utils.mustache.r_sa_check")(template,...)Do a final check to see if a tag could be a standalone.
[`utils.mustache.render`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.mustache.render.html#langchain_core.utils.mustache.render "langchain_core.utils.mustache.render")([template,data,...])Render a mustache template.
[`utils.mustache.tokenize`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.mustache.tokenize.html#langchain_core.utils.mustache.tokenize "langchain_core.utils.mustache.tokenize")(template[,...])Tokenize a mustache template.
[`utils.pydantic.create_model`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.create_model.html#langchain_core.utils.pydantic.create_model "langchain_core.utils.pydantic.create_model")(model_name[,...])Create a pydantic model with the given field definitions.
[`utils.pydantic.create_model_v2`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.create_model_v2.html#langchain_core.utils.pydantic.create_model_v2 "langchain_core.utils.pydantic.create_model_v2")(model_name,*)Create a pydantic model with the given field definitions.
[`utils.pydantic.get_fields`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.get_fields.html#langchain_core.utils.pydantic.get_fields "langchain_core.utils.pydantic.get_fields")()Get the field names of a Pydantic model.
[`utils.pydantic.get_pydantic_major_version`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.get_pydantic_major_version.html#langchain_core.utils.pydantic.get_pydantic_major_version "langchain_core.utils.pydantic.get_pydantic_major_version")()DEPRECATED - Get the major version of Pydantic.
[`utils.pydantic.is_basemodel_instance`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.is_basemodel_instance.html#langchain_core.utils.pydantic.is_basemodel_instance "langchain_core.utils.pydantic.is_basemodel_instance")(obj)Check if the given class is an instance of Pydantic BaseModel.
[`utils.pydantic.is_basemodel_subclass`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.is_basemodel_subclass.html#langchain_core.utils.pydantic.is_basemodel_subclass "langchain_core.utils.pydantic.is_basemodel_subclass")(cls)Check if the given class is a subclass of Pydantic BaseModel.
[`utils.pydantic.is_pydantic_v1_subclass`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.is_pydantic_v1_subclass.html#langchain_core.utils.pydantic.is_pydantic_v1_subclass "langchain_core.utils.pydantic.is_pydantic_v1_subclass")(cls)Check if the installed Pydantic version is 1.x-like.
[`utils.pydantic.is_pydantic_v2_subclass`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.is_pydantic_v2_subclass.html#langchain_core.utils.pydantic.is_pydantic_v2_subclass "langchain_core.utils.pydantic.is_pydantic_v2_subclass")(cls)Check if the installed Pydantic version is 1.x-like.
[`utils.pydantic.pre_init`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.pydantic.pre_init.html#langchain_core.utils.pydantic.pre_init "langchain_core.utils.pydantic.pre_init")(func)Decorator to run a function before model initialization.
[`utils.strings.comma_list`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.strings.comma_list.html#langchain_core.utils.strings.comma_list "langchain_core.utils.strings.comma_list")(items)Convert a list to a comma-separated string.
[`utils.strings.stringify_dict`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.strings.stringify_dict.html#langchain_core.utils.strings.stringify_dict "langchain_core.utils.strings.stringify_dict")(data)Stringify a dictionary.
[`utils.strings.stringify_value`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.strings.stringify_value.html#langchain_core.utils.strings.stringify_value "langchain_core.utils.strings.stringify_value")(val)Stringify a value.
[`utils.utils.build_extra_kwargs`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.build_extra_kwargs.html#langchain_core.utils.utils.build_extra_kwargs "langchain_core.utils.utils.build_extra_kwargs")(extra_kwargs,...)Build extra kwargs from values and extra_kwargs.
[`utils.utils.check_package_version`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.check_package_version.html#langchain_core.utils.utils.check_package_version "langchain_core.utils.utils.check_package_version")(package[,...])Check the version of a package.
[`utils.utils.convert_to_secret_str`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.convert_to_secret_str.html#langchain_core.utils.utils.convert_to_secret_str "langchain_core.utils.utils.convert_to_secret_str")(value)Convert a string to a SecretStr if needed.
[`utils.utils.from_env`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.from_env.html#langchain_core.utils.utils.from_env "langchain_core.utils.utils.from_env")()Create a factory method that gets a value from an environment variable.
[`utils.utils.get_pydantic_field_names`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.get_pydantic_field_names.html#langchain_core.utils.utils.get_pydantic_field_names "langchain_core.utils.utils.get_pydantic_field_names")(...)Get field names, including aliases, for a pydantic class.
[`utils.utils.guard_import`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.guard_import.html#langchain_core.utils.utils.guard_import "langchain_core.utils.utils.guard_import")(module_name,*[,...])Dynamically import a module.
[`utils.utils.mock_now`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.mock_now.html#langchain_core.utils.utils.mock_now "langchain_core.utils.utils.mock_now")(dt_value)Context manager for mocking out datetime.now() in unit tests.
[`utils.utils.raise_for_status_with_text`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.raise_for_status_with_text.html#langchain_core.utils.utils.raise_for_status_with_text "langchain_core.utils.utils.raise_for_status_with_text")(response)Raise an error with the response text.
[`utils.utils.secret_from_env`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.secret_from_env.html#langchain_core.utils.utils.secret_from_env "langchain_core.utils.utils.secret_from_env")()Secret from env.
[`utils.utils.xor_args`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.utils.xor_args.html#langchain_core.utils.utils.xor_args "langchain_core.utils.utils.xor_args")(*arg_groups)Validate specified keyword args are mutually exclusive.".

**Deprecated functions**

[`utils.function_calling.convert_pydantic_to_openai_function`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.convert_pydantic_to_openai_function.html#langchain_core.utils.function_calling.convert_pydantic_to_openai_function "langchain_core.utils.function_calling.convert_pydantic_to_openai_function")(...)
[`utils.function_calling.convert_pydantic_to_openai_tool`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.convert_pydantic_to_openai_tool.html#langchain_core.utils.function_calling.convert_pydantic_to_openai_tool "langchain_core.utils.function_calling.convert_pydantic_to_openai_tool")(...)
[`utils.function_calling.convert_python_function_to_openai_function`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.convert_python_function_to_openai_function.html#langchain_core.utils.function_calling.convert_python_function_to_openai_function "langchain_core.utils.function_calling.convert_python_function_to_openai_function")(...)
[`utils.function_calling.format_tool_to_openai_function`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.format_tool_to_openai_function.html#langchain_core.utils.function_calling.format_tool_to_openai_function "langchain_core.utils.function_calling.format_tool_to_openai_function")(tool)
[`utils.function_calling.format_tool_to_openai_tool`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.function_calling.format_tool_to_openai_tool.html#langchain_core.utils.function_calling.format_tool_to_openai_tool "langchain_core.utils.function_calling.format_tool_to_openai_tool")(tool)
[`utils.loading.try_load_from_hub`](https://python.langchain.com/api_reference/core/utils/langchain_core.utils.loading.try_load_from_hub.html#langchain_core.utils.loading.try_load_from_hub "langchain_core.utils.loading.try_load_from_hub")(*args,**kwargs)

[vectorstores](https://python.langchain.com/api_reference/core/vectorstores.html#langchain-core-vectorstores)[#](https://python.langchain.com/api_reference/core/index.html#langchain-core-vectorstores "Link to this heading")
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

**Classes**

**Functions**
