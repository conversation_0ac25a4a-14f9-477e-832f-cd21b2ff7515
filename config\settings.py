"""
Configuration settings for the Image Generation Agent.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings:
    """Configuration settings for the Image Generation Agent."""

    # OpenAI API Configuration
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

    # Black Forest Labs (BFL) API Configuration
    # Used for FLUX.1 Kontext image generation/editing
    BFL_API_KEY: str = os.getenv("BFL_API_KEY", "")  # Header: x-key
    PROVIDER_DEFAULT: str = os.getenv("PROVIDER_DEFAULT", "bfl")  # Default provider for image flows
    BFL_DEFAULT_MODEL: str = os.getenv("BFL_DEFAULT_MODEL", "kontext_pro")  # 'kontext_pro' | 'kontext_max'
    BFL_ASPECT_DEFAULT: str = os.getenv("BFL_ASPECT_DEFAULT", "1:1")
    # Mapping of legacy size strings to BFL aspect ratios
    SIZE_TO_ASPECT = {
        "1024x1024": "1:1",
        "1536x1024": "3:2",
        "1024x1536": "2:3",
        "auto": "1:1",
    }

    # Model Configuration
    LANGUAGE_MODEL: str = os.getenv("LANGUAGE_MODEL", "gpt-4o-mini")
    IMAGE_MODEL: str = os.getenv("IMAGE_MODEL", "gpt-image-1")

    # Default Image Generation Parameters
    DEFAULT_IMAGE_SIZE: str = os.getenv("DEFAULT_IMAGE_SIZE", "auto")
    DEFAULT_IMAGE_QUALITY: str = os.getenv("DEFAULT_IMAGE_QUALITY", "auto")
    DEFAULT_OUTPUT_FORMAT: str = os.getenv("DEFAULT_OUTPUT_FORMAT", "auto")
    DEFAULT_BACKGROUND: str = os.getenv("DEFAULT_BACKGROUND", "auto")
    DEFAULT_MODERATION: str = os.getenv("DEFAULT_MODERATION", "auto")
    DEFAULT_OUTPUT_COMPRESSION: int = int(os.getenv("DEFAULT_OUTPUT_COMPRESSION", "100"))

    # Application Configuration
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # File Storage Configuration
    GENERATED_IMAGES_DIR: str = os.getenv("GENERATED_IMAGES_DIR", "generated_images")

    # Prompt Enhancement Configuration
    PROMPT_ENHANCEMENT_MODEL: str = os.getenv("PROMPT_ENHANCEMENT_MODEL", "gpt-4o-mini")
    PROMPT_ENHANCEMENT_ENABLED: bool = os.getenv("PROMPT_ENHANCEMENT_ENABLED", "True").lower() == "true"
    PROMPT_ENHANCEMENT_MAX_TOKENS: int = int(os.getenv("PROMPT_ENHANCEMENT_MAX_TOKENS", "500"))

    @classmethod
    def validate_settings(cls) -> bool:
        """Validate that required settings are present."""
        # Image workflows can run purely on BFL; LLM features require OpenAI.
        # Consider the installation valid if at least one provider key is present.
        if not cls.OPENAI_API_KEY and not cls.BFL_API_KEY:
            raise ValueError("At least one API key is required: set BFL_API_KEY for image workflows and/or OPENAI_API_KEY for LLM features.")
        return True

    @classmethod
    def validate(cls) -> bool:
        """Validate all settings (alias for validate_settings for compatibility)."""
        return cls.validate_settings()
