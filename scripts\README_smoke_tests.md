Smoke test helpers

- smoke_test_edit_responses.py: Posts a tools/call to the MCP SSE endpoint using image_edit_responses. Supply a base64 text file as the first arg.

Usage

1) Start containers so the SSE MCP server is on http://localhost:8000
2) Save your base64 of a PNG/JPEG into a file, e.g. tmp\img_b64.txt
3) Run:
   - Windows PowerShell:
     $env:MCP_URL = "http://localhost:8000/messages"; python scripts/smoke_test_edit_responses.py tmp\img_b64.txt

Expected result

- HTTP 200 JSON-RPC response containing a resource with a file:// URI pointing to generated_images/...
- An image saved in the generated_images folder with a filename like responses_api_YYYMMDD_...png
