# Migration Plan: OpenAI Images/Responses ➜ FLUX.1 Kontext (pro/max)

This plan describes how to replace OpenAI image generation/editing with Black Forest Labs’ FLUX.1 Kontext models (pro and max) for all image workflows. The MCP server will not depend on OpenAI after this change. The web app may retain non-image OpenAI agent capabilities if needed. It covers current architecture, official BFL API specifics (docs.bfl.ai + api.bfl.ai), a file-by-file refactor plan, testing, and rollout.

## Executive summary
- Goal: Replace OpenAI image paths with FLUX.1 Kontext [pro] and [max] for contextual image editing (and optional T2I). MCP image tools must be BFL-only; OpenAI may remain only for non-image agent features in the web app.
- Provider: Black Forest Labs API (BFL) — official endpoints on https://api.bfl.ai.
- Documentation: use #Context7 MCP tools to pull "BFL AI" as the library name (`"context7CompatibleLibraryID": "/websites/bfl_ai"`)
- Approach: Implement BFL image tool(s), wire into Agent, Web, and MCP. Expose only BFL-backed image tools from MCP. Default provider is 'bfl' for all image operations.
- Remove OpenAI image tools from MCP exposure and stop routing image requests to OpenAI; retain only non-image OpenAI usage in the web app if required.

## Current system overview (as-is)
- Agent and tools
  - Primary advanced path: `src/responses_image_tool.py` uses OpenAI Responses API with image_generation tool; supports multi-turn, streaming partials, reference images, optional mask via `input_image_mask`.
  - Legacy paths: `src/image_generation_tool.py` and `src/image_edit_tool.py` call OpenAI `gpt-image-1` for direct generate/edit.
  - Orchestration: `src/agent.py` binds tools through LangChain; prompt/system rules prefer Responses path; custom rulesets supported.
- Web app: `web_app.py`
  - POST `/api/chat` routes to the agent; image uploads processed via `utils/image_encoder.ImageEncoder`.
  - Mode hints: parameters.api_mode injects hints to prefer Responses vs legacy.
- MCP server: `src/mcp/server.py` and `src/mcp/tools.py`
  - SSE-only transport; exposes five tools: image_generate_legacy, image_edit_legacy, image_generate_responses, image_edit_responses, image_edit_mask_responses.
  - Resource root is `generated_images/` (saved files, embedded resources).
- Configuration: `config/settings.py`
  - `.env`-driven; OpenAI API key required; default models set; default image params centralized.
- Utilities: `utils/image_encoder.py` handles validation, base64 encoding, reference image prep, and uploads processing.

## FLUX API (official BFL) — what we’ll integrate
- OpenAPI: https://api.bfl.ai/openapi.json
- Documentation hub: https://docs.bfl.ai/ (see Kontext overview and editing guides)
- Model endpoints (BFL):
  - FLUX.1 Kontext [pro]: `POST https://api.bfl.ai/v1/flux-kontext-pro`
  - FLUX.1 Kontext [max]: `POST https://api.bfl.ai/v1/flux-kontext-max`
- Result polling and webhooks:
  - `GET https://api.bfl.ai/v1/get_result?id=<task_id>` returns a status and result payload
  - Most generation endpoints are asynchronous and return `{ id, polling_url }`; optionally provide `webhook_url` and `webhook_secret` for push-style completion
- Auth: API key in header using `x-key: <BFL_API_KEY>` (server-side only)
- Inputs (Kontext image-to-image and T2I):
  - Primary schema `FluxKontextProInputs` includes:
    - `prompt: string` (required)
    - `input_image: string | null` (base64 string or URL; also `input_image_2`..`input_image_4` for multi-reference, marked experimental)
    - `seed?: int`
    - `aspect_ratio?: string` (supported range approx 3:7 to 7:3; defaults to 1:1 if omitted; outputs are ~1MP)
    - `output_format?: 'jpeg' | 'png'` (default varies by endpoint; typically 'jpeg' — set explicitly to 'png' if you need lossless)
    - `prompt_upsampling?: boolean` (default false)
    - `safety_tolerance?: int` (0..6, default 2)
    - `webhook_url?: string`, `webhook_secret?: string`
  - Notes:
    - Kontext focuses on contextual image generation/editing with optional multi-reference images. Masks are not part of the Kontext inputs; use Fill/Expand endpoints for strict masking and outpainting.
- Related endpoints for edits and controls (optional later phases):
  - Fill (inpainting/outpainting with mask): `POST /v1/flux-pro-1.0-fill`
    - Inputs include `image` (base64), optional `mask` (base64; same dims), `prompt`, `steps`, `guidance`, `output_format`, `safety_tolerance`.
  - Expand (outpaint by padding sides): `POST /v1/flux-pro-1.0-expand`
  - Control (canny/depth): `POST /v1/flux-pro-1.0-canny`, `POST /v1/flux-pro-1.0-depth` (and finetuned variants)
- Outputs:
  - Initial response: Async with `{ id, polling_url }` (or webhook acknowledgment)
  - Polling response: `{ id, status, result?, progress?, details?, preview? }` with `status` in { Pending, Ready, Error, Request Moderated, Content Moderated }
  - The `result` shape can vary by endpoint; for image tasks it includes the generated image(s) (URL and/or base64). We will handle both URL and base64 outputs defensively.
- Streaming: No partial image frames. Prefer async polling or webhooks; optionally surface preview/progress if returned.

## OpenAI → FLUX parameter mapping
- Prompt: same (`prompt`).
- Base image: OpenAI `image_b64`/`image_uri` → BFL `input_image` (accepts base64 string or URL). Prefer raw base64 (no data URL prefix) for reliability.
- Size/aspect:
  - Current app sizes ("1024x1024", "1536x1024", "1024x1536") map to Kontext `aspect_ratio` strings (e.g., 1:1, 3:2, 2:3). If exact pixel control is required (for non-Kontext endpoints), some models support `width`/`height`.
  - Respect BFL-supported aspect ratio range (about 3:7 to 7:3); keep total pixels around 1MP as per model behavior.
- Quality/background/moderation:
  - OpenAI `quality` and `background` do not apply. BFL provides `safety_tolerance` (0..6). Map app-level `moderation` to a default `safety_tolerance` (e.g., 2) and expose as an advanced parameter.
- Multi-turn and streaming:
  - OpenAI multi-turn via `previous_response_id` has no direct BFL equivalent. Treat Kontext calls as stateless per request.
  - Streaming partial images: not supported; use async polling/webhooks and optionally surface `preview`/`progress` from the result response if present.
- Mask:
  - Kontext does not take masks. For inpainting/outpainting, route to BFL Fill (`/v1/flux-pro-1.0-fill`) or Expand (`/v1/flux-pro-1.0-expand`). Ensure the mask dims match the image.

## Refactor plan (file-by-file)
1) Configuration (`config/settings.py`)
- Add:
  - `BFL_API_KEY: str | None` (header `x-key`)
  - `BFL_DEFAULT_MODEL: Literal['kontext_pro','kontext_max']` (default `kontext_pro`)
  - `BFL_ASPECT_DEFAULT: '1:1'` and mappings for our existing sizes → aspect ratios
  - `PROVIDER_DEFAULT: 'bfl'` (BFL is the default for all image flows). If you still support non-image OpenAI features in the web app, keep their keys optional.
- Validation: image workflows require `BFL_API_KEY`. OpenAI key is optional and only used for non-image features (if present).

2) New tool: BFL Kontext (`src/bfl_kontext_tool.py`)
- Implement a `FluxKontextTool` (BaseTool) with Pydantic input schema:
  - Fields: `prompt: str`, `image_b64?: str`, `image_uri?: str`, `aspect_ratio?: str`, `seed?: int`, `output_format?: 'jpeg'|'png'`, `safety_tolerance?: int`, `prompt_upsampling?: bool`, `webhook_url?: str`, `webhook_secret?: str`, `model_variant?: 'pro'|'max'`.
- Behavior:
  - Resolve base image: prefer `image_b64` (raw base64 string). If `image_uri` is a local file path, read and encode to base64; if it’s an http(s) URL, pass as-is.
  - Submit to BFL API using REST:
    - POST to `/v1/flux-kontext-pro` or `/v1/flux-kontext-max` with JSON body matching `FluxKontextProInputs` fields, header `x-key`.
    - Expect an async response `{ id, polling_url }`; either poll `/v1/get_result?id=...` until `status==='Ready'` or accept a `webhook_url` flow.
  - When `status==='Ready'`, extract the generated image(s) from `result` — handle both URL and base64. Download or decode and save to `generated_images/` using timestamp naming (e.g., `bfl_kontext_pro_YYYYMMDD_HHMMSS_<prompt_snippet>.jpg/png`).
  - Return a text summary including saved path and task id; surface moderation statuses (`Request Moderated`, `Content Moderated`) and errors.
- Dependencies: add `requests` (or `httpx`) to `requirements.txt`.

3) Agent integration (`src/agent.py`)
- Bind `FluxKontextTool` into the toolset alongside existing tools.
- System prompt: add routing guidance
  - E.g., if user or parameters indicate provider=bfl or contains a tag (e.g., `[use_bfl]`), prefer BFL Kontext tool.
- Runtime selection:
  - Route all image tasks to BFL by default. Remove/disable routing to OpenAI image tools. If non-image OpenAI capabilities exist, keep them unaffected.

4) MCP server and tools (`src/mcp/tools.py`, `src/mcp/server.py`)
- Add new MCP tools:
  - `image_edit_bfl_kontext` (pro by default, parameter to choose max)
  - Optionally `image_edit_bfl_kontext_max` shortcut
- Input schema: align to BFL fields for Kontext (no masks): `prompt`, `image_b64?`, `image_uri?`, `aspect_ratio?`, `seed?`, `output_format?`, `safety_tolerance?`, `prompt_upsampling?`, `bfl_model?`.
- Output: Attach the saved file as an EmbeddedResource (`file://…/generated_images/...`).
- Resources: no changes; continue exposing `generated_images/`.
- Deprecate/stop exposing OpenAI image tools (legacy and responses) from MCP list_tools so the MCP wrapper is BFL-only for images.

5) Web app (`web_app.py`)
- Parameters handling:
  - Accept `provider` and `bfl_model` ("pro"|"max") in `parameters` JSON.
  - Default to `provider==='bfl'` for all image flows; inject a hint to the agent to use the BFL Kontext tool and pass through relevant params (aspect_ratio, safety_tolerance, prompt_upsampling, etc.).
- Image upload: no changes required; `ImageEncoder` already returns data URLs we can pass as `image_url`.
- Response payload: consistent with current chat endpoint; return summarization text. Optionally include the saved image path.

6) Utilities (`utils/image_encoder.py`)
- No required changes; ensure the encoder can produce data URLs for base images and masks. If mask is used, ensure dimensions match base image.
- For BFL, prefer raw base64 strings without the `data:` prefix for `input_image` and `mask` fields.

7) Tests (`tests/`)
- Add tests for:
  - MCP tool discovery includes only BFL image tools (OpenAI image tools removed/hidden).
  - MCP `image_edit_bfl_kontext` happy path: mocked BFL REST calls returning async id then a Ready result with either URL or base64; verify saved file and response resource.
  - Web `/api/chat` routing to provider=bfl: with a small base64 image; assert success text and file creation.
  - Error propagation: simulate BFL API error and confirm the tool returns text with the error (no exceptions). Cover moderated statuses.
- Testing pattern: mirror existing mock-heavy approach (`@patch` HTTP calls), low-side effects; avoid network calls.

## Detailed implementation guidance
- Contract for FluxKontextTool
  - Input: prompt + base image (b64 or uri), optional aspect_ratio, seed, output_format, safety_tolerance, prompt_upsampling, and model_variant.
  - Output: string text that includes the saved image path(s) and task id. On error/moderation, return a prefixed message string capturing status and details.
  - Error modes: missing BFL key; missing prompt or image; HTTP error; polling timeout; moderated statuses; unexpected `result` shape.
  - Success criteria: saved image exists, reasonable filename, parameters reflected in summary; robust against URL or base64 results.

- Provider/size mapping
  - Map current sizes to aspect ratios:
    - 1024x1024 → 1:1
    - 1536x1024 → 3:2 (approx) or explicit `{ width: 1536, height: 1024 }`
    - 1024x1536 → 2:3 (approx) or explicit `{ width: 1024, height: 1536 }`
  - Prefer aspect ratios when only context edits are required; use explicit sizes if exact dims are essential.

- REST invocation sketch (Python)
  - Submit: POST `https://api.bfl.ai/v1/flux-kontext-pro` (or `/v1/flux-kontext-max`) with JSON body `{ "prompt": ..., "input_image": <base64 or URL>, "aspect_ratio": ..., "output_format": "png", ... }` and header `x-key: <BFL_API_KEY>`.
  - Poll: GET `https://api.bfl.ai/v1/get_result?id=<task_id>` with header `x-key: <BFL_API_KEY>` until `status` is `Ready` or `Error` (handle `Request Moderated` / `Content Moderated`).
  - Download/Decode: If `result` contains URL(s), GET and save; if base64, decode and save using the appropriate extension.

- Handling masks
  - Kontext: masks are not part of the inputs. Route inpainting/outpainting requests to `POST /v1/flux-pro-1.0-fill` (with `image` and optional `mask`) or `POST /v1/flux-pro-1.0-expand` (for padding-based outpainting).
  - Ensure mask dimensions match the base image; validate with Pillow if provided.

- Streaming/logs
  - No partial frames; rely on polling or webhooks. Optionally surface `progress`/`preview` fields if present.

- Storage and naming
  - Save as `generated_images/bfl_kontext_<variant>_YYYYMMDD_HHMMSS_<prompt_snippet>.<ext>`.
  - Maintain compatibility with resource listing and `/images/<filename>` endpoint.

## Rollout plan (phased)
1) Config scaffolding
  - Add BFL env vars, defaults, and validation branches. Document in README and `.env.example` (if present).
2) Tool implementation
  - Create `src/bfl_kontext_tool.py`; add dependency (`requests`/`httpx`) to `requirements.txt`.
  - Unit tests for the tool with mocked HTTP.
3) MCP wiring
  - Add new MCP tool(s) in `src/mcp/tools.py`; update list_tools and call dispatch; tests for tool discovery and call flow.
4) Agent + web routing
  - Bind tool in `src/agent.py`; extend `web_app.py` parameter handling and UI hints (if needed) to select provider/model.
  - Smoke test via `scripts/` similar to existing `smoke_test_edit_responses.py`.
5) Documentation
  - Add a short "Using FLUX (BFL API)" section to README with env vars and a sample JSON payload for `/api/chat`.
6) Optional: feature flags
  - Add `FEATURE_FLUX_ENABLED` switch to Settings to gate the provider in production.

## Testing strategy
- Unit tests
  - Mocked BFL REST for submit/status/result pipeline (happy path and each failure mode).
  - Image download mocked to a tiny fake response; verify write path and content type handling.
- Integration tests (web and MCP)
  - MCP: call the new tool with a data URL base image; assert an EmbeddedResource is returned.
  - Web: POST `/api/chat` with `{ parameters: { provider: 'bfl', bfl_model: 'pro' }, images: [...] }`; assert summary includes saved file path.
- Regression tests
  - Ensure any existing non-image OpenAI flows (if present) remain green; no OpenAI image tools should be invoked.

## Breaking changes and mitigations
- Multi-turn image editing: BFL Kontext has no conversation continuity or streaming partials; treat as stateless per request. Mitigation: remove multi-turn image editing; keep any non-image multi-turn agent features if relevant.
- Streaming partial images: not available. Mitigation: surface logs or progress updates; no partial image files.
- Mask support variance: Kontext lacks mask fields; use BFL Fill/Expand endpoints for precise masking/outpainting. Mitigation: keep OpenAI edit with mask as an alternative if needed.
- Moderation differences: OpenAI `moderation` vs BFL `safety_tolerance`. Mitigation: add a simple mapping and configurable default; document differences.

## Example payloads and snippets
- Minimal Kontext call (conceptual REST JSON input)
  - `POST https://api.bfl.ai/v1/flux-kontext-pro`
  - Body:
    - `{ "prompt": "Add a donut on the table.", "input_image": "<base64>", "aspect_ratio": "1:1", "output_format": "png", "safety_tolerance": 2 }`
  - Response: `{ "id": "...", "polling_url": "https://api.bfl.ai/v1/get_result?id=..." }`
  - Poll result: when `status === "Ready"`, extract images from `result` (URL or base64) and save.

## Next steps checklist
- Add config keys and defaults.
- Implement `FluxKontextTool` (BFL) with async submit + polling and file saving.
- Wire new MCP tools and update list/call handlers.
- Bind tool in Agent and extend web parameter handling for provider selection.
- Add tests (tool, MCP, web).
- Update README and perform smoke tests.

## References
- BFL API OpenAPI (official): https://api.bfl.ai/openapi.json
- BFL Docs hub: https://docs.bfl.ai/
- Kontext overview/editing: https://docs.bfl.ai/kontext/kontext_overview, https://docs.bfl.ai/kontext/kontext_image_editing
- Quick start/generating images: https://docs.bfl.ai/quick_start/generating_images
