import base64
import json
import os
import sys
import requests

MCP_URL = os.environ.get("MCP_URL", "http://localhost:8000/messages")

payload = {
    "method": "tools/call",
    "params": {
        "name": "image_edit_responses",
        "arguments": {
            "prompt": "Keep the same overall color theme and lighting, but transform the mountain scene into a serene sunrise beach scene. Replace mountains with a sandy shoreline, gentle ocean waves, and distant horizon. Maintain the hues, contrast, and atmospheric mood. Add subtle wet sand reflections near the waterline and a few soft clouds.",
            "image_b64": None,
            "size": "1024x1024",
            "quality": "low",
            "background": "opaque",
            "moderation": "auto",
            "show_revised_prompt": True
        }
    },
    "jsonrpc": "2.0",
    "id": 1
}

if __name__ == "__main__":
    # Allow passing a base64 file path via CLI
    if len(sys.argv) > 1:
        b64_path = sys.argv[1]
        with open(b64_path, 'r', encoding='utf-8') as f:
            payload["params"]["arguments"]["image_b64"] = f.read().strip()
    else:
        print("Usage: python scripts/smoke_test_edit_responses.py <path_to_base64.txt>")
        sys.exit(2)

    resp = requests.post(MCP_URL, json=payload, headers={"Content-Type": "application/json"})
    print("Status:", resp.status_code)
    try:
        print(json.dumps(resp.json(), indent=2)[:4000])
    except Exception:
        print(resp.text[:2000])
