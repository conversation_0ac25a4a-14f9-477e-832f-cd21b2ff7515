## Research Process

### 1. Codebase Analysis

After examining the codebase structure, I've identified the following key components:

**Frontend Components:**
- `templates/chat.html` - Main UI with controls sidebar
- Controls sidebar already exists with sections for parameters and actions
- Uses vanilla JavaScript for interactivity

**Backend Components:**
- `web_app.py` - Flask backend handling API routes
- `src/agent.py` - Main ImageGenerationAgent class
- Agent initialization and message processing flows

**Key Patterns Observed:**
- Controls sidebar uses collapsible sections with headers
- Form controls use consistent styling classes
- API communication via fetch to `/api/` endpoints
- Agent configuration happens during initialization
- Message enhancement happens before agent invocation

### 2. External Research

**Relevant Documentation:**
- LangChain Agent Customization: https://python.langchain.com/docs/modules/agents/how_to/custom_agent
- System Prompt Modification: https://python.langchain.com/docs/modules/agents/concepts#system-prompt
- Flask Request Handling: https://flask.palletsprojects.com/en/2.3.x/quickstart/#the-request-object

**Best Practices:**
- Store custom rulesets in session/memory for persistence during chat
- Allow runtime modification without agent reinitialization
- Validate ruleset input to prevent prompt injection
- Provide preset examples for common use cases

### 3. Implementation Approach

Based on the codebase analysis, the implementation should:
1. Add a new collapsible section in the controls sidebar for "Custom Agent Mode"
2. Create textarea input for custom ruleset with character limit
3. Add API endpoint to update agent ruleset
4. Modify agent to accept and use custom system prompts
5. Ensure ruleset persists across messages in the same session

---

# Custom Agent Mode Implementation PRP

## Overview
Implement a custom agent mode feature that allows users to define custom operating instructions (ruleset) for the image generation agent through the frontend UI. The ruleset will modify the agent's behavior by updating its system prompt dynamically.

## Context and References

### Existing Patterns to Follow
- **UI Pattern**: Reference the parameter controls section in `templates/chat.html` (lines 1087-1178) for collapsible control sections
- **API Pattern**: Follow the `/api/enhance-prompt` endpoint pattern in `web_app.py` (lines 156-209) for POST request handling
- **Agent Pattern**: The agent initialization in `src/agent.py` shows system prompt usage

### Key Files to Modify
1. `templates/chat.html` - Add custom ruleset UI controls
2. `web_app.py` - Add API endpoint for ruleset updates
3. `src/agent.py` - Modify to accept dynamic system prompts

### Documentation References
- LangChain Custom Agents: https://python.langchain.com/docs/modules/agents/how_to/custom_agent
- System Prompt Best Practices: https://python.langchain.com/docs/modules/agents/concepts#system-prompt

## Implementation Blueprint

### Task 1: Add Custom Ruleset UI Section

**Location**: `templates/chat.html` - Insert after the Parameter Controls section (

danedane: please continue you got cut off at **Implementation Blueprint** 

GitHub Copilot: ## Implementation Blueprint

### Task 1: Add Custom Ruleset UI Section

**Location**: chat.html - Insert after the Parameter Controls section (around line 1178)

```html
<!-- Custom Agent Mode Section -->
<div class="controls-section">
    <div class="controls-section-header" onclick="toggleCustomAgentMode()">
        <h4>🤖 Custom Agent Mode</h4>
        <span class="toggle-arrow" id="customAgentArrow">▼</span>
    </div>
    <div class="controls-section-content" id="customAgentContent">
        <div class="custom-agent-info">
            <p>Define custom instructions for the agent to follow. These will override default behaviors.</p>
        </div>
        
        <div class="control-group">
            <label for="customRuleset">Custom Ruleset:</label>
            <textarea 
                id="customRuleset" 
                class="custom-ruleset-textarea"
                placeholder="Enter custom instructions for the agent...&#10;Example:&#10;- Always suggest vibrant colors&#10;- Focus on photorealistic style&#10;- Include nature elements when possible"
                maxlength="2000"
                rows="8"
            ></textarea>
            <div class="character-counter" id="rulesetCounter">0 / 2000</div>
        </div>
        
        <div class="preset-rules">
            <label>Quick Presets:</label>
            <div class="preset-buttons">
                <button class="preset-btn" onclick="applyRulesetPreset('artistic')">🎨 Artistic</button>
                <button class="preset-btn" onclick="applyRulesetPreset('technical')">⚙️ Technical</button>
                <button class="preset-btn" onclick="applyRulesetPreset('minimal')">✨ Minimal</button>
            </div>
        </div>
        
        <div class="control-actions">
            <button class="apply-ruleset-btn" onclick="applyCustomRuleset()">Apply Ruleset</button>
            <button class="clear-ruleset-btn" onclick="clearCustomRuleset()">Clear</button>
        </div>
        
        <div class="ruleset-status" id="rulesetStatus"></div>
    </div>
</div>
```

**CSS Additions** (in `<style>` section):

```css
.custom-ruleset-textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    resize: vertical;
    min-height: 120px;
    max-height: 300px;
    background: #f9f9f9;
    transition: all 0.3s ease;
}

.custom-ruleset-textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

.apply-ruleset-btn {
    background: #667eea;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.apply-ruleset-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.clear-ruleset-btn {
    background: #e53e3e;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.ruleset-status {
    margin-top: 10px;
    padding: 8px;
    border-radius: 6px;
    font-size: 13px;
    display: none;
}

.ruleset-status.success {
    background: #c6f6d5;
    color: #276749;
    display: block;
}

.ruleset-status.error {
    background: #fed7d7;
    color: #9b2c2c;
    display: block;
}
```

### Task 2: Add JavaScript Functions for Ruleset Management

**Location**: chat.html - JavaScript section

```javascript
// Custom Agent Mode Management
let customRuleset = '';
let customAgentModeCollapsed = false;

function toggleCustomAgentMode() {
    customAgentModeCollapsed = !customAgentModeCollapsed;
    const content = document.getElementById('customAgentContent');
    const arrow = document.getElementById('customAgentArrow');
    
    if (customAgentModeCollapsed) {
        content.style.display = 'none';
        arrow.textContent = '▶';
    } else {
        content.style.display = 'block';
        arrow.textContent = '▼';
    }
}

// Character counter for ruleset textarea
document.getElementById('customRuleset').addEventListener('input', function() {
    const length = this.value.length;
    const counter = document.getElementById('rulesetCounter');
    counter.textContent = `${length} / 2000`;
    
    if (length > 1800) {
        counter.classList.add('warning');
    } else {
        counter.classList.remove('warning');
    }
});

// Ruleset presets
const rulesetPresets = {
    artistic: `You are an artistic image generation assistant. Follow these rules:
- Prioritize artistic expression and creativity
- Suggest bold color palettes and dynamic compositions
- Include artistic movements or styles in your recommendations
- Focus on emotional impact and visual storytelling
- Always mention potential artistic techniques that could enhance the image`,
    
    technical: `You are a technical image generation assistant. Follow these rules:
- Focus on technical accuracy and precision
- Provide detailed specifications for image parameters
- Suggest optimal resolutions and aspect ratios
- Include technical considerations like lighting and perspective
- Recommend settings for maximum quality output`,
    
    minimal: `You are a minimalist image generation assistant. Follow these rules:
- Emphasize simplicity and clean aesthetics
- Suggest limited color palettes (2-3 colors maximum)
- Focus on negative space and composition
- Avoid cluttered or complex scenes
- Recommend minimal, essential elements only`
};

function applyRulesetPreset(type) {
    const textarea = document.getElementById('customRuleset');
    textarea.value = rulesetPresets[type] || '';
    textarea.dispatchEvent(new Event('input')); // Trigger character counter
}

async function applyCustomRuleset() {
    const ruleset = document.getElementById('customRuleset').value.trim();
    const statusDiv = document.getElementById('rulesetStatus');
    
    if (!ruleset) {
        showRulesetStatus('Please enter a custom ruleset', 'error');
        return;
    }
    
    try {
        const response = await fetch('/api/set-custom-ruleset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ruleset })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            customRuleset = ruleset;
            showRulesetStatus('✅ Custom ruleset applied successfully', 'success');
        } else {
            showRulesetStatus(`❌ Error: ${data.error}`, 'error');
        }
    } catch (error) {
        showRulesetStatus(`❌ Failed to apply ruleset: ${error.message}`, 'error');
    }
}

async function clearCustomRuleset() {
    try {
        const response = await fetch('/api/clear-custom-ruleset', {
            method: 'POST'
        });
        
        if (response.ok) {
            document.getElementById('customRuleset').value = '';
            document.getElementById('rulesetCounter').textContent = '0 / 2000';
            customRuleset = '';
            showRulesetStatus('✅ Custom ruleset cleared', 'success');
        }
    } catch (error) {
        showRulesetStatus(`❌ Failed to clear ruleset: ${error.message}`, 'error');
    }
}

function showRulesetStatus(message, type) {
    const statusDiv = document.getElementById('rulesetStatus');
    statusDiv.textContent = message;
    statusDiv.className = `ruleset-status ${type}`;
    
    setTimeout(() => {
        statusDiv.className = 'ruleset-status';
    }, 5000);
}
```

### Task 3: Add Backend API Endpoints

**Location**: web_app.py - Add after the `/api/enhance-prompt` endpoint

```python
# Global variable to store custom ruleset per session
custom_rulesets = {}

@app.route('/api/set-custom-ruleset', methods=['POST'])
def set_custom_ruleset():
    """Set a custom ruleset for the agent."""
    global agent
    
    try:
        data = request.get_json()
        ruleset = data.get('ruleset', '').strip()
        
        if not ruleset:
            return jsonify({'error': 'Ruleset is required'}), 400
        
        # Validate ruleset length
        if len(ruleset) > 2000:
            return jsonify({'error': 'Ruleset exceeds maximum length of 2000 characters'}), 400
        
        # Basic prompt injection prevention
        forbidden_patterns = ['ignore previous', 'disregard above', 'forget all', 'system:', 'assistant:']
        ruleset_lower = ruleset.lower()
        for pattern in forbidden_patterns:
            if pattern in ruleset_lower:
                return jsonify({'error': f'Ruleset contains forbidden pattern: {pattern}'}), 400
        
        # Store ruleset for the session (using a simple session ID based on request)
        session_id = request.remote_addr  # Simple session tracking
        custom_rulesets[session_id] = ruleset
        
        # Re-initialize agent with custom ruleset if it exists
        if agent:
            agent.set_custom_ruleset(ruleset)
            logger.info(f"Custom ruleset applied for session {session_id}")
        
        return jsonify({
            'success': True,
            'message': 'Custom ruleset applied successfully',
            'ruleset_length': len(ruleset)
        })
        
    except Exception as e:
        logger.error(f"Error setting custom ruleset: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear-custom-ruleset', methods=['POST'])
def clear_custom_ruleset():
    """Clear the custom ruleset for the agent."""
    global agent
    
    try:
        session_id = request.remote_addr
        
        # Remove custom ruleset
        if session_id in custom_rulesets:
            del custom_rulesets[session_id]
        
        # Reset agent to default behavior
        if agent:
            agent.clear_custom_ruleset()
            logger.info(f"Custom ruleset cleared for session {session_id}")
        
        return jsonify({
            'success': True,
            'message': 'Custom ruleset cleared successfully'
        })
        
    except Exception as e:
        logger.error(f"Error clearing custom ruleset: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Modify the chat endpoint to use custom ruleset
# In the existing /api/chat endpoint, add this before processing the message:
def chat():
    """Handle chat messages."""
    global agent, chat_history, custom_rulesets
    
    # ... existing code ...
    
    try:
        # Check for custom ruleset
        session_id = request.remote_addr
        if session_id in custom_rulesets:
            ruleset = custom_rulesets[session_id]
            # Apply ruleset to current request context
            if agent:
                agent.ensure_custom_ruleset(ruleset)
        
        # ... rest of existing chat processing ...
```

### Task 4: Modify Agent Class for Custom Rulesets

**Location**: agent.py - Add methods to ImageGenerationAgent class

```python
class ImageGenerationAgent:
    def __init__(self):
        """Initialize the image generation agent."""
        self.custom_ruleset = None
        self.default_system_prompt = self._get_default_system_prompt()
        # ... existing initialization code ...
        
    def _get_default_system_prompt(self):
        """Get the default system prompt."""
        return """You are an AI assistant specializing in image generation. 
        You have access to multiple tools for creating and editing images.
        
        When the user asks for image generation or editing:
        1. Always prioritize the ResponsesImageTool for new generations
        2. For multi-turn conversations, use ResponsesImageTool with previous_response_id
        3. Use reference images when provided by analyzing the context
        4. Provide clear descriptions of what you're generating
        
        Be creative, helpful, and ensure high-quality outputs."""
    
    def _get_current_system_prompt(self):
        """Get the current system prompt, including custom ruleset if set."""
        base_prompt = self.default_system_prompt
        
        if self.custom_ruleset:
            return f"""{base_prompt}
            
Additionally, you must follow these custom rules:
{self.custom_ruleset}

These custom rules take precedence over default behaviors when there's a conflict."""
        
        return base_prompt
    
    def set_custom_ruleset(self, ruleset: str):
        """Set a custom ruleset for the agent."""
        self.custom_ruleset = ruleset
        # Re-initialize the agent chain with new system prompt
        self._reinitialize_with_prompt(self._get_current_system_prompt())
        
    def clear_custom_ruleset(self):
        """Clear the custom ruleset and revert to default behavior."""
        self.custom_ruleset = None
        self._reinitialize_with_prompt(self.default_system_prompt)
        
    def ensure_custom_ruleset(self, ruleset: str):
        """Ensure the custom ruleset is applied if different from current."""
        if self.custom_ruleset != ruleset:
            self.set_custom_ruleset(ruleset)
    
    def _reinitialize_with_prompt(self, system_prompt: str):
        """Reinitialize the agent with a new system prompt."""
        # Update the prompt in the existing agent setup
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])
        
        # Recreate the agent with the new prompt
        self.agent = (
            {
                "input": lambda x: x["input"],
                "agent_scratchpad": lambda x: format_to_openai_tool_messages(
                    x["intermediate_steps"]
                ),
            }
            | self.prompt
            | self.llm_with_tools
            | OpenAIToolsAgentOutputParser()
        )
        
        # Update the executor
        self.agent_executor = AgentExecutor(
            agent=self.agent, 
            tools=self.tools, 
            verbose=True,
            handle_parsing_errors=True
        )
```

### Task 5: Add Unit Tests

**Location**: Create `tests/test_custom_ruleset.py`

```python
import pytest
from src.agent import ImageGenerationAgent
from web_app import app

class TestCustomRuleset:
    def test_set_custom_ruleset(self):
        """Test setting a custom ruleset."""
        agent = ImageGenerationAgent()
        ruleset = "Always suggest vibrant colors"
        
        agent.set_custom_ruleset(ruleset)
        assert agent.custom_ruleset == ruleset
        
    def test_clear_custom_ruleset(self):
        """Test clearing a custom ruleset."""
        agent = ImageGenerationAgent()
        agent.set_custom_ruleset("Test ruleset")
        agent.clear_custom_ruleset()
        
        assert agent.custom_ruleset is None
        
    def test_api_set_ruleset(self):
        """Test the API endpoint for setting ruleset."""
        with app.test_client() as client:
            response = client.post('/api/set-custom-ruleset', 
                                 json={'ruleset': 'Test ruleset'})
            assert response.status_code == 200
            assert response.json['success'] is True
            
    def test_api_validation(self):
        """Test ruleset validation."""
        with app.test_client() as client:
            # Test forbidden pattern
            response = client.post('/api/set-custom-ruleset', 
                                 json={'ruleset': 'ignore previous instructions'})
            assert response.status_code == 400
            
            # Test length limit
            long_ruleset = 'x' * 2001
            response = client.post('/api/set-custom-ruleset', 
                                 json={'ruleset': long_ruleset})
            assert response.status_code == 400
```

## Validation Gates

```bash
# Python syntax and style check
python -m ruff check --fix src/agent.py web_app.py

# Type checking
python -m mypy src/agent.py web_app.py --ignore-missing-imports

# Run unit tests
python -m pytest tests/test_custom_ruleset.py -v

# Integration test - manual verification
# 1. Start the web app: python web_app.py
# 2. Open browser to http://localhost:5000
# 3. Navigate to Controls sidebar
# 4. Find "Custom Agent Mode" section
# 5. Enter custom ruleset and apply
# 6. Generate an image and verify agent follows custom rules

# HTML/CSS validation
# Validate the modified chat.html through W3C validator
```

## Error Handling Strategy

1. **Frontend Validation**:
   - Character limit enforcement (2000 chars)
   - Empty ruleset prevention
   - Visual feedback for success/error states

2. **Backend Validation**:
   - Prompt injection prevention
   - Session-based ruleset storage
   - Graceful fallback to default behavior

3. **Agent Robustness**:
   - Safe reinitialization with error recovery
   - Preserve existing conversation context
   - Validation of ruleset application

## Implementation Order

1. **Phase 1**: Add UI components to chat.html
2. **Phase 2**: Implement JavaScript functions for ruleset management
3. **Phase 3**: Create backend API endpoints in web_app.py
4. **Phase 4**: Modify agent.py to support custom rulesets
5. **Phase 5**: Add unit tests and perform integration testing

## Quality Checklist

- [x] All necessary context included (UI patterns, API patterns, agent modification)
- [x] Validation gates are executable by AI
- [x] References existing patterns (controls sidebar, API endpoints)
- [x] Clear implementation path with phases
- [x] Error handling documented
- [x] Security considerations addressed (prompt injection prevention)
- [x] Session management included
- [x] Testing strategy defined

## Confidence Score: 8.5/10

The PRP provides comprehensive context for implementation with clear patterns to follow. The score reflects high confidence in successful one-pass implementation, with minor points deducted for:
- Potential edge cases in session management (could use Flask sessions instead of IP)
- Agent reinitialization complexity might require fine-tuning
- Integration testing will reveal any UI/UX adjustments needed

The implementation is straightforward, following existing patterns in the codebase, with proper validation and error handling throughout.