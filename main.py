"""
Main application for the Langchain Image Generation Agent.
"""
import sys
import os
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage, AIMessage
from src.agent import ImageGenerationAgent
from config.settings import Settings
import logging
from utils.image_encoder import ImageEncoder

# Load environment variables from .env file
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImageGenerationApp:
    """Main application class for the Image Generation Agent."""
    
    def __init__(self):
        """Initialize the application."""
        logger.info("Starting Image Generation Agent Application...")
        try:
            self.agent = ImageGenerationAgent()
            self.chat_history = []
            self.reference_images = []  # Track loaded reference images
            logger.info("Application initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize application: {str(e)}")
            raise
    
    def add_to_history(self, user_message: str, agent_response: str):
        """Add messages to chat history."""
        self.chat_history.append(HumanMessage(content=user_message))
        self.chat_history.append(AIMessage(content=agent_response))
        
        # Keep only last 10 messages to avoid context length issues
        if len(self.chat_history) > 20:
            self.chat_history = self.chat_history[-20:]
    
    def run_interactive(self):
        """Enhanced interactive mode with Responses API features."""
        print("\n" + "="*80)
        print("🎨 Langchain Image Generation Agent (Responses API Enhanced)")
        print("="*80)
        print("🚀 NEW ADVANCED FEATURES:")
        print("  • Multi-turn editing: Continue and refine your images iteratively")
        print("  • Reference images: Use your own images for style and content guidance")
        print("  • Streaming generation: Watch your images being created in real-time")
        print("  • Intelligent prompting: AI-optimized prompts for better results")
        print("\n📋 ENHANCED COMMANDS:")
        print("  refs [path1] [path2]...  - Load reference images for guidance")
        print("  continue [instruction]   - Edit the last generated image")
        print("  stream [prompt]         - Generate with streaming partial results")
        print("  clear-refs              - Remove all loaded reference images")
        print("  history                 - Show generation history with IDs")
        print("\n🔧 STANDARD COMMANDS:")
        print("  clear                   - Clear chat history")
        print("  test                    - Run system tests")
        print("  exit/quit               - End the session")
        print("\n💡 USAGE EXAMPLES:")
        print("  refs style.jpg logo.png")
        print("  Generate a modern website banner")
        print("  continue make it more colorful")
        print("  stream Create a detailed fantasy landscape")
        print("="*80)
        
        while True:
            try:
                # Show reference status
                ref_status = f" [{len(self.reference_images)} refs]" if self.reference_images else ""
                user_input = input(f"\n🧑 You{ref_status}: ").strip()
                
                # Handle exit commands
                if user_input.lower() in ['exit', 'quit']:
                    print("\n👋 Goodbye! Thanks for using the Image Generation Agent!")
                    break
                
                # Handle clear chat history
                elif user_input.lower() == 'clear':
                    self.chat_history = []
                    print("\n🧹 Chat history cleared!")
                    continue
                
                # Handle system tests
                elif user_input.lower() == 'test':
                    print("\n🧪 Running system tests...")
                    self.run_tests()
                    continue
                
                # Handle reference image loading
                elif user_input.startswith('refs '):
                    self._handle_reference_loading(user_input)
                    continue
                
                # Handle clear references
                elif user_input.lower() == 'clear-refs':
                    self._handle_clear_references()
                    continue
                
                # Handle generation history
                elif user_input.lower() == 'history':
                    self._handle_history_display()
                    continue
                
                # Handle continue/multi-turn commands
                elif self._is_continue_command(user_input):
                    self._handle_continue_command(user_input)
                    continue
                
                # Handle streaming generation
                elif user_input.startswith('stream '):
                    self._handle_streaming_generation(user_input)
                    continue
                
                # Handle empty input
                elif not user_input:
                    print("Please enter a message or command.")
                    continue
                
                # Handle regular generation with enhanced features
                self._handle_regular_generation(user_input)
                
            except KeyboardInterrupt:
                print("\n\n👋 Session interrupted. Goodbye!")
                break
            except Exception as e:
                logger.error(f"Error in interactive session: {str(e)}")
                print(f"\n❌ An error occurred: {str(e)}")

    def _handle_reference_loading(self, user_input: str):
        """Handle loading reference images."""
        paths = user_input[5:].split()  # Remove 'refs ' prefix
        
        if not paths:
            print("Usage: refs [path1] [path2] ...")
            print("Example: refs style.jpg reference.png")
            return
        
        valid_refs = []
        invalid_refs = []
        
        print(f"\n📎 Loading {len(paths)} reference image(s)...")
        
        for path in paths:
            # Expand relative paths
            full_path = os.path.abspath(path)
            
            if os.path.exists(full_path):
                if ImageEncoder.validate_image(full_path):
                    valid_refs.append(full_path)
                    print(f"  ✅ {os.path.basename(full_path)} - Valid")
                else:
                    invalid_refs.append(path)
                    print(f"  ❌ {os.path.basename(path)} - Invalid format/size")
            else:
                invalid_refs.append(path)
                print(f"  ❌ {path} - File not found")
        
        # Add valid references
        if valid_refs:
            self.reference_images.extend(valid_refs)
            print(f"\n✅ Successfully loaded {len(valid_refs)} reference image(s)")
            print(f"📊 Total references: {len(self.reference_images)}")
            
            # Show reference summary
            print("\n📋 Current references:")
            for i, ref in enumerate(self.reference_images, 1):
                print(f"  {i}. {os.path.basename(ref)}")
        
        if invalid_refs:
            print(f"\n⚠️  {len(invalid_refs)} image(s) could not be loaded")
            print("💡 Supported formats: PNG, JPG, JPEG, WEBP (max 50MB)")

    def _handle_clear_references(self):
        """Handle clearing reference images."""
        if self.reference_images:
            count = len(self.reference_images)
            self.reference_images = []
            print(f"\n🧹 Cleared {count} reference image(s)!")
        else:
            print("\n📭 No reference images to clear.")

    def _handle_history_display(self):
        """Handle displaying generation history."""
        # Access history from the responses tool
        responses_tool = None
        for tool in self.agent.tools:
            if hasattr(tool, 'name') and tool.name == 'ResponsesImageTool':
                responses_tool = tool
                break
        
        if responses_tool and hasattr(responses_tool, 'response_history') and responses_tool.response_history:
            print("\n📜 Recent Generation History:")
            print("-" * 60)
            
            # Show last 10 generations
            recent_history = responses_tool.response_history[-10:]
            for i, resp in enumerate(recent_history, 1):
                status_icon = "↳" if resp.get('is_continuation') else "🆕"
                response_id = resp.get('id', 'N/A')[:8]  # Show first 8 chars
                prompt_preview = resp.get('prompt', '')[:50]
                
                print(f"{i:2d}. {status_icon} [{response_id}] {prompt_preview}...")
            
            print(f"\nShowing {len(recent_history)} of {len(responses_tool.response_history)} total generations")
            
            if responses_tool.last_response_id:
                print(f"🔗 Current session ID: {responses_tool.last_response_id[:8]}")
        else:
            print("\n📜 No generation history available yet.")
            print("💡 Generate some images to see history here!")

    def _is_continue_command(self, user_input: str) -> bool:
        """Check if user input is a continue/multi-turn command."""
        continue_patterns = [
            'continue ',
            'edit previous ',
            'edit the previous ',
            'modify last ',
            'modify the last ',
            'improve the ',
            'change the ',
            'make it ',
            'now make it ',
        ]
        
        user_lower = user_input.lower()
        return any(user_lower.startswith(pattern) for pattern in continue_patterns)

    def _handle_continue_command(self, user_input: str):
        """Handle continue/multi-turn generation commands."""
        # Extract instruction from various continue patterns
        user_lower = user_input.lower()
        
        if user_lower.startswith('continue '):
            instruction = user_input[9:]  # Remove 'continue '
        elif user_lower.startswith('edit previous '):
            instruction = user_input[14:]  # Remove 'edit previous '
        elif user_lower.startswith('edit the previous '):
            instruction = user_input[18:]  # Remove 'edit the previous '
        elif user_lower.startswith('modify last '):
            instruction = user_input[12:]  # Remove 'modify last '
        elif user_lower.startswith('modify the last '):
            instruction = user_input[16:]  # Remove 'modify the last '
        elif user_lower.startswith(('improve the ', 'change the ', 'make it ', 'now make it ')):
            # For these patterns, use the full input as instruction
            instruction = user_input
        else:
            instruction = user_input
        
        if not instruction.strip():
            instruction = "improve the image"
        
        print(f"\n🔄 Continuing from previous image...")
        print(f"📝 Instruction: {instruction}")
        
        # Build enhanced prompt for multi-turn
        enhanced_input = f"Use ResponsesImageTool with continue_previous=True and prompt='{instruction}'"
        
        # Add reference images if available
        if self.reference_images:
            enhanced_input += f" and reference_images={self.reference_images}"
            print(f"📎 Including {len(self.reference_images)} reference image(s)")
        
        print("\n🤖 Agent: ", end="", flush=True)
        response = self.agent.invoke(enhanced_input, self.chat_history)
        print(response)
        
        # Add to history
        self.add_to_history(user_input, response)

    def _handle_streaming_generation(self, user_input: str):
        """Handle streaming generation commands."""
        prompt = user_input[7:]  # Remove 'stream ' prefix
        
        if not prompt.strip():
            print("Usage: stream [prompt]")
            print("Example: stream Create a detailed fantasy landscape")
            return
        
        print(f"\n🎬 Starting streaming generation...")
        print(f"📝 Prompt: {prompt}")
        
        # Build enhanced prompt for streaming
        enhanced_input = f"Use ResponsesImageTool with stream_partial=True and prompt='{prompt}'"
        
        # Add reference images if available
        if self.reference_images:
            enhanced_input += f" and reference_images={self.reference_images}"
            print(f"📎 Including {len(self.reference_images)} reference image(s)")
        
        print("\n🤖 Agent: ", end="", flush=True)
        response = self.agent.invoke(enhanced_input, self.chat_history)
        print(response)
        
        # Add to history
        self.add_to_history(user_input, response)

    def _handle_regular_generation(self, user_input: str):
        """Handle regular generation with automatic feature detection."""
        print("\n🤖 Agent: ", end="", flush=True)
        
        # Check if this is an image generation request
        image_keywords = [
            'generate', 'create', 'make', 'draw', 'design', 'produce',
            'image', 'picture', 'photo', 'illustration', 'artwork',
            'painting', 'sketch', 'render', 'visualize'
        ]
        
        is_image_request = any(keyword in user_input.lower() for keyword in image_keywords)
        
        if is_image_request:
            print("🎨 Generating with Responses API...")
            
            # Build enhanced prompt with automatic ResponsesImageTool usage
            enhanced_input = f"Use ResponsesImageTool with prompt='{user_input}'"
            
            # Add reference images if available
            if self.reference_images:
                enhanced_input += f" and reference_images={self.reference_images}"
                print(f"\n📎 Using {len(self.reference_images)} reference image(s)")
            
            response = self.agent.invoke(enhanced_input, self.chat_history)
        else:
            # Regular conversation
            response = self.agent.invoke(user_input, self.chat_history)
        
        print(response)
        
        # Add to history
        self.add_to_history(user_input, response)
    
    def run_single_query(self, query: str) -> str:
        """
        Run a single query and return the response.
        
        Args:
            query: The user's query
            
        Returns:
            str: The agent's response
        """
        try:
            response = self.agent.invoke(query, self.chat_history)
            self.add_to_history(query, response)
            return response
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def run_tests(self):
        """Run comprehensive system tests."""
        print("\n🔍 Running comprehensive tests...")
        
        tests = [
            ("Configuration", self._test_configuration),
            ("LLM Integration", self._test_llm),
            ("Image Generation Tool", self._test_image_generation_tool),
            ("Image Editing Tool", self._test_image_editing_tool),
            ("Agent Functionality", self._test_agent),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                print(f"\n  Testing {test_name}...", end=" ")
                result = test_func()
                if result:
                    print("✓ PASSED")
                    results.append(True)
                else:
                    print("✗ FAILED")
                    results.append(False)
            except Exception as e:
                print(f"✗ ERROR: {str(e)}")
                results.append(False)
        
        # Summary
        passed = sum(results)
        total = len(results)
        print(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! The system is ready to use.")
        else:
            print("⚠️  Some tests failed. Please check the configuration and try again.")
    
    def _test_configuration(self) -> bool:
        """Test configuration settings."""
        return Settings.validate()
    
    def _test_llm(self) -> bool:
        """Test LLM integration."""
        return self.agent.llm_integration.test_connectivity()
    
    def _test_image_generation_tool(self) -> bool:
        """Test image generation tool."""
        return self.agent.image_generator.test_generation()
    
    def _test_image_editing_tool(self) -> bool:
        """Test image editing tool."""
        return self.agent.image_editor.test_editing()
    
    def _test_agent(self) -> bool:
        """Test agent functionality."""
        return self.agent.test_agent()

def main():
    """Main entry point."""
    try:
        # Check if running with arguments
        if len(sys.argv) > 1:
            if sys.argv[1] == "test":
                app = ImageGenerationApp()
                app.run_tests()
            else:
                # Run with a single query
                query = " ".join(sys.argv[1:])
                app = ImageGenerationApp()
                response = app.run_single_query(query)
                print(f"\nQuery: {query}")
                print(f"Response: {response}")
        else:
            # Run interactive mode
            app = ImageGenerationApp()
            app.run_interactive()
            
    except Exception as e:
        logger.error(f"Application failed to start: {str(e)}")
        print(f"\n❌ Failed to start application: {str(e)}")
        print("\nPlease check:")
        print("1. Your OpenAI API key is set in the .env file")
        print("2. All required packages are installed")
        print("3. You have internet connectivity")
        sys.exit(1)

if __name__ == "__main__":
    main()
