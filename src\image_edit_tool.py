"""
Custom Langchain tool for image editing using OpenAI's gpt-image-1 model.
"""
from typing import Optional, Type, List, Union
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
import openai
from config.settings import Settings
import logging
import base64

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageEditInput(BaseModel):
    """Input schema for the image editing tool."""
    prompt: str = Field(description="Text description of how to edit the image (max 32000 characters)")
    image_data: str = Field(description="Base64 encoded image data to edit")
    mask_data: Optional[str] = Field(
        default=None,
        description="Base64 encoded mask image data (optional, for inpainting)"
    )
    size: Optional[str] = Field(
        default=Settings.DEFAULT_IMAGE_SIZE,
        description="Size of the output image ('auto', '1024x1024', '1536x1024', '1024x1536')"
    )
    quality: Optional[str] = Field(
        default=Settings.DEFAULT_IMAGE_QUALITY,
        description="Quality of the image ('auto', 'high', 'medium', 'low')"
    )
    output_format: Optional[str] = Field(
        default=Settings.DEFAULT_OUTPUT_FORMAT,
        description="Output format ('png', 'jpeg', or 'webp')"
    )
    background: Optional[str] = Field(
        default=Settings.DEFAULT_BACKGROUND,
        description="Background setting ('auto', 'transparent', 'opaque')"
    )
    moderation: Optional[str] = Field(
        default=Settings.DEFAULT_MODERATION,
        description="Content moderation level ('auto' for standard, 'low' for less restrictive)"
    )

class OpenAIImageEditor(BaseTool):
    """Custom Langchain tool for editing images using OpenAI's gpt-image-1 model."""

    name: str = "OpenAIImageEditor"
    description: str = (
        "Tool to edit existing images using OpenAI's gpt-image-1 model. "
        "Can modify images based on text prompts, create new images using existing ones as reference, "
        "or perform inpainting with mask data. Input requires base64 image data and editing instructions. "
        "If no image_data is provided, will use uploaded reference images if available."
    )
    args_schema: Type[BaseModel] = ImageEditInput

    def __init__(self):
        """Initialize the image editing tool."""
        super().__init__()
        # Use private attribute to avoid Pydantic validation
        self._processed_images = None
        logger.info(f"Initialized OpenAI Image Editor with model: {Settings.IMAGE_MODEL}")

    @property
    def processed_images(self):
        """Get processed images."""
        return self._processed_images

    @processed_images.setter
    def processed_images(self, value):
        """Set processed images."""
        self._processed_images = value
    
    def _get_client(self):
        """Get the OpenAI client instance."""
        return openai.OpenAI(api_key=Settings.OPENAI_API_KEY)

    def set_processed_images(self, processed_images: dict):
        """Set processed images from uploads for use in editing."""
        self.processed_images = processed_images
        if processed_images and processed_images.get('success_count', 0) > 0:
            logger.info(f"Image editor now has access to {processed_images['success_count']} processed images")
    
    def _run(
        self,
        prompt: str,
        image_data: str,
        mask_data: Optional[str] = None,
        size: Optional[str] = None,
        quality: Optional[str] = None,
        output_format: Optional[str] = None,
        background: Optional[str] = None,
        moderation: Optional[str] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """
        Execute the image editing tool.
        
        Args:
            prompt: Text description of how to edit the image
            image_data: Base64 encoded image data
            mask_data: Base64 encoded mask data (optional)
            size: Image size (optional)
            quality: Image quality (optional)
            output_format: Output format (optional)
            background: Background setting (optional)
            moderation: Moderation level (optional)
            run_manager: Callback manager (optional)
            
        Returns:
            str: Base64 encoded edited image data with metadata or error message
        """
        try:
            # Use defaults if not provided
            size = size or Settings.DEFAULT_IMAGE_SIZE
            quality = quality or Settings.DEFAULT_IMAGE_QUALITY
            output_format = output_format or Settings.DEFAULT_OUTPUT_FORMAT
            background = background or Settings.DEFAULT_BACKGROUND
            moderation = moderation or Settings.DEFAULT_MODERATION
            
            logger.info(f"Editing image with prompt: '{prompt[:50]}...'")
            logger.info(f"Parameters - Size: {size}, Quality: {quality}, Format: {output_format}")
            logger.info(f"Background: {background}, Moderation: {moderation}")
            logger.info(f"Mask provided: {'Yes' if mask_data else 'No'}")

            # Prepare image data - use uploaded images if no image_data provided
            if not image_data and self.processed_images and self.processed_images.get('base_images'):
                # Use the first uploaded image as the base image
                image_data = self.processed_images['base_images'][0]
                logger.info("Using uploaded reference image as base image for editing")
            elif not image_data:
                return "Error: No image data provided and no uploaded images available. Please provide an image to edit."

            # Prepare image data
            image_bytes = base64.b64decode(image_data)
            
            # Build API parameters
            api_params = {
                "model": Settings.IMAGE_MODEL,
                "image": [image_bytes],  # gpt-image-1 accepts list of images
                "prompt": prompt,
                "size": size,
                "quality": quality,
                "output_format": output_format,
                "background": background,
                "moderation": moderation,
                "n": 1
            }
            
            # Add mask if provided
            if mask_data:
                mask_bytes = base64.b64decode(mask_data)
                api_params["mask"] = mask_bytes
            
            # Get client and make API call to gpt-image-1
            client = self._get_client()
            response = client.images.edit(**api_params)
            
            # gpt-image-1 returns base64-encoded images
            edited_image_b64 = response.data[0].b64_json
            
            # Get token usage information if available
            usage_info = ""
            if hasattr(response, 'usage') and response.usage:
                usage = response.usage
                usage_info = f"\nToken Usage - Total: {usage.total_tokens}, Input: {usage.input_tokens}, Output: {usage.output_tokens}"
            
            logger.info(f"Successfully edited image with gpt-image-1{usage_info}")
            
            result_msg = f"""Image edited successfully with gpt-image-1!
✓ Format: {output_format.upper()}
✓ Size: {size}
✓ Quality: {quality}
✓ Background: {background}
✓ Editing type: {'Inpainting (with mask)' if mask_data else 'General editing'}
✓ Base64 length: {len(edited_image_b64)} characters{usage_info}

The edited image is ready for use or further editing."""
            
            return result_msg
            
        except openai.OpenAIError as e:
            error_msg = f"OpenAI API error: {str(e)}"
            logger.error(error_msg)
            return f"Error editing image: {error_msg}"
        
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return f"Error editing image: {error_msg}"
    
    async def _arun(
        self,
        prompt: str,
        image_data: str,
        mask_data: Optional[str] = None,
        size: Optional[str] = None,
        quality: Optional[str] = None,
        output_format: Optional[str] = None,
        background: Optional[str] = None,
        moderation: Optional[str] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """
        Async version of the tool execution.
        For now, we'll use the sync version.
        """
        return self._run(prompt, image_data, mask_data, size, quality, output_format, background, moderation, run_manager)
    
    def test_editing(self, test_prompt: str = "Make the image brighter", test_image_b64: str = None) -> bool:
        """
        Test the image editing functionality.
        
        Args:
            test_prompt: Simple prompt for testing
            test_image_b64: Base64 encoded test image (optional)
            
        Returns:
            bool: True if test successful, False otherwise
        """
        try:
            if not test_image_b64:
                # Create a simple test image (1x1 white pixel in base64)
                test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            
            result = self._run(test_prompt, test_image_b64)
            if "Error" not in result:
                logger.info("Image editing test passed")
                return True
            else:
                logger.error(f"Image editing test failed: {result}")
                return False
        except Exception as e:
            logger.error(f"Image editing test failed with exception: {str(e)}")
            return False

# Test function for standalone usage
def test_image_edit_tool():
    """Test the image editing tool."""
    try:
        tool = OpenAIImageEditor()
        
        # Test with a simple prompt and test image
        if tool.test_editing():
            print("✓ Image editing tool test passed")
            return True
        else:
            print("✗ Image editing tool test failed")
            return False
    except Exception as e:
        print(f"✗ Image editing tool test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    test_image_edit_tool()