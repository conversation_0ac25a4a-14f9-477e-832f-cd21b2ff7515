import unittest
import os
from unittest import TestCase
from unittest.mock import patch

from src.mcp import tools as mcp_tools
from src.bfl_kontext_tool import FluxKontextTool
from config.settings import Settings


class TestMcpBflTools(TestCase):
    def setUp(self):
        self.tmp_dir = os.path.abspath(os.path.join(os.getcwd(), 'generated_images_test'))
        os.makedirs(self.tmp_dir, exist_ok=True)
        # Snapshot env and settings
        self._prev_bfl = os.environ.get("BFL_API_KEY")
        self._prev_gen = os.environ.get("GENERATED_IMAGES_DIR")
        self._settings_snapshot = (Settings.BFL_API_KEY, Settings.GENERATED_IMAGES_DIR)

    def tearDown(self):
        # Restore env
        if self._prev_bfl is None:
            os.environ.pop("BFL_API_KEY", None)
        else:
            os.environ["BFL_API_KEY"] = self._prev_bfl
        if self._prev_gen is None:
            os.environ.pop("GENERATED_IMAGES_DIR", None)
        else:
            os.environ["GENERATED_IMAGES_DIR"] = self._prev_gen
        Settings.BFL_API_KEY, Settings.GENERATED_IMAGES_DIR = self._settings_snapshot

    def test_list_tools_only_bfl(self):
        exposed = mcp_tools.list_tools()
        names = {t['name'] for t in exposed}
        self.assertEqual(names, {"image_generate_bfl_kontext", "image_edit_bfl_kontext"})

    @patch.object(FluxKontextTool, '_submit')
    @patch.object(FluxKontextTool, '_poll')
    @patch.object(FluxKontextTool, '_save_result_payload')
    def test_generate_happy_path(self, mock_save, mock_poll, mock_submit):
        os.environ["BFL_API_KEY"] = "dummy"
        os.environ["GENERATED_IMAGES_DIR"] = self.tmp_dir
        Settings.BFL_API_KEY = "dummy"
        Settings.GENERATED_IMAGES_DIR = self.tmp_dir

        mock_submit.return_value = {"id": "abc123", "polling_url": "https://api.bfl.ai/v1/get_result?id=abc123"}
        mock_poll.return_value = {"status": "Ready", "result": {"images": [{"b64": "aGVsbG8="}]}}
        out_file = os.path.join(self.tmp_dir, "bfl_kontext_pro_20250101_000000_test.png")
        mock_save.return_value = out_file

        out = mcp_tools.call_image_generate_bfl_kontext({
            "prompt": "a test image",
            "aspect_ratio": "1:1",
            "output_format": "png",
            "bfl_model": "pro",
        })

        texts = [o.get('text') for o in out if o.get('type') == 'text']
        resources = [o.get('resource') for o in out if o.get('type') == 'resource']
        self.assertTrue(any("Saved to:" in (t or "") for t in texts))
        self.assertTrue(resources and resources[0]['uri'].startswith('file://'))

    @patch.object(FluxKontextTool, '_submit')
    @patch.object(FluxKontextTool, '_poll')
    @patch.object(FluxKontextTool, '_save_result_payload')
    def test_edit_happy_path_with_file_and_b64(self, mock_save, mock_poll, mock_submit):
        os.environ["BFL_API_KEY"] = "dummy"
        Settings.BFL_API_KEY = "dummy"
        os.environ["GENERATED_IMAGES_DIR"] = self.tmp_dir
        Settings.GENERATED_IMAGES_DIR = self.tmp_dir

        base_file = os.path.join(self.tmp_dir, "base.png")
        with open(base_file, 'wb') as f:
            f.write(b"fake")

        mock_submit.return_value = {"id": "abc124", "polling_url": "https://api.bfl.ai/v1/get_result?id=abc124"}
        mock_poll.return_value = {"status": "Ready", "result": {"images": [{"b64": "aGVsbG8="}]}}
        out_file = os.path.join(self.tmp_dir, "bfl_kontext_pro_20250101_000001_edit.png")
        mock_save.return_value = out_file

        out_plain = mcp_tools.call_image_edit_bfl_kontext({
            "prompt": "edit colors",
            "image_uri": base_file,
        })
        res_plain = [o.get('resource') for o in out_plain if o.get('type') == 'resource']
        self.assertTrue(res_plain)

        out_uri = mcp_tools.call_image_edit_bfl_kontext({
            "prompt": "add fog",
            "image_uri": f"file://{base_file}",
        })
        res_uri = [o.get('resource') for o in out_uri if o.get('type') == 'resource']
        self.assertTrue(res_uri)

        import base64 as b64mod
        with open(base_file, 'rb') as f:
            b64 = b64mod.b64encode(f.read()).decode('utf-8')
        out_b64 = mcp_tools.call_image_edit_bfl_kontext({
            "prompt": "increase contrast",
            "image_b64": b64,
        })
        res_b64 = [o.get('resource') for o in out_b64 if o.get('type') == 'resource']
        self.assertTrue(res_b64)

    def test_errors_missing_prompt_and_base(self):
        out = mcp_tools.call_image_generate_bfl_kontext({})
        texts = [o.get('text') for o in out if o.get('type') == 'text']
        self.assertTrue(any("'prompt' is required" in (t or "") for t in texts))

        out2 = mcp_tools.call_image_edit_bfl_kontext({"prompt": "x"})
        texts2 = [o.get('text') for o in out2 if o.get('type') == 'text']
        self.assertTrue(any("'image_b64' or 'image_uri' is required" in (t or "") for t in texts2))

    def test_resolve_image_path_windows_styles(self):
        os.environ["GENERATED_IMAGES_DIR"] = self.tmp_dir
        Settings.GENERATED_IMAGES_DIR = self.tmp_dir
        from src.mcp.tools import _resolve_image_path as resolve2
        fpath = os.path.join(self.tmp_dir, "responses_api_20250101_test.png")
        with open(fpath, 'w', encoding='utf-8') as f:
            f.write('x')
        win_like = "C\\\\fake\\\\path\\\\responses_api_20250101_test.png"
        p = resolve2(win_like)
        self.assertTrue(p and p.endswith("responses_api_20250101_test.png"))

    @patch.object(FluxKontextTool, '_submit')
    @patch.object(FluxKontextTool, '_poll')
    @patch.object(FluxKontextTool, '_save_result_payload')
    def test_invalid_aspect_and_safety_bounds(self, mock_save, mock_poll, mock_submit):
        os.environ["BFL_API_KEY"] = "dummy"
        Settings.BFL_API_KEY = "dummy"
        os.environ["GENERATED_IMAGES_DIR"] = self.tmp_dir
        Settings.GENERATED_IMAGES_DIR = self.tmp_dir

        mock_submit.return_value = {"id": "abc125", "polling_url": "https://api.bfl.ai/v1/get_result?id=abc125"}
        mock_poll.return_value = {"status": "Ready", "result": {"images": [{"b64": "aGVsbG8="}]}}
        out_file = os.path.join(self.tmp_dir, "bfl_kontext_pro_20250101_000002_test.png")
        mock_save.return_value = out_file

        out = mcp_tools.call_image_generate_bfl_kontext({
            "prompt": "x",
            "aspect_ratio": "9:0",
            "safety_tolerance": 0,
            "prompt_upsampling": True,
        })
        texts = [o.get('text') for o in out if o.get('type') == 'text']
        self.assertTrue(texts)

        out2 = mcp_tools.call_image_generate_bfl_kontext({
            "prompt": "x",
            "aspect_ratio": "1:1",
            "safety_tolerance": 6,
            "prompt_upsampling": False,
        })
        texts2 = [o.get('text') for o in out2 if o.get('type') == 'text']
        self.assertTrue(texts2)

    def test_missing_bfl_key(self):
        os.environ.pop("BFL_API_KEY", None)
        Settings.BFL_API_KEY = ""
        out = mcp_tools.call_image_generate_bfl_kontext({"prompt": "x"})
        texts = [o.get('text') for o in out if o.get('type') == 'text']
        self.assertTrue(any("BFL_API_KEY" in (t or "") for t in texts))

    def test_invalid_image_uri_and_quoted_paths(self):
        # Ensure env set so tool proceeds to resolve path
        os.environ["BFL_API_KEY"] = "dummy"
        Settings.BFL_API_KEY = "dummy"
        os.environ["GENERATED_IMAGES_DIR"] = self.tmp_dir
        Settings.GENERATED_IMAGES_DIR = self.tmp_dir

        # Invalid path should trigger base-image-required error
        out = mcp_tools.call_image_edit_bfl_kontext({
            "prompt": "edit test",
            "image_uri": "C\\\\nope\\\\missing.png",
        })
        texts = [o.get('text') for o in out if o.get('type') == 'text']
        self.assertTrue(any("image_b64' or 'image_uri' is required" in (t or "") for t in texts))

        # Quoted path basename should resolve within generated_images
        fname = "responses_api_20250101_quote.png"
        fpath = os.path.join(self.tmp_dir, fname)
        with open(fpath, 'wb') as f:
            f.write(b"x")
        quoted = f'"C\\\\fake\\\\dir\\\\{fname}"'
        # Patch FluxKontextTool to short-circuit network and saving
        with patch.object(FluxKontextTool, '_submit') as m_sub, \
             patch.object(FluxKontextTool, '_poll') as m_pol, \
             patch.object(FluxKontextTool, '_save_result_payload') as m_save:
            m_sub.return_value = {"id": "abc126", "polling_url": "https://api.bfl.ai/v1/get_result?id=abc126"}
            m_pol.return_value = {"status": "Ready", "result": {"images": [{"b64": "aGVsbG8="}]}}
            m_save.return_value = os.path.join(self.tmp_dir, "bfl_kontext_pro_20250101_000003_quote.png")
            out2 = mcp_tools.call_image_edit_bfl_kontext({
                "prompt": "use quoted path",
                "image_uri": quoted,
            })
            res = [o.get('resource') for o in out2 if o.get('type') == 'resource']
            self.assertTrue(res)


if __name__ == '__main__':
    unittest.main()
