<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 AI Image Generation Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden; /* Prevent body scroll */
        }

        .main-container {
            width: 98%;
            max-width: 1200px;
            height: 90vh;
            display: flex;
            flex-direction: row;
            gap: 15px;
            align-items: stretch;
        }

        .chat-container {
            flex: 1;
            min-width: 0; /* Allow flex item to shrink below content size */
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .controls-sidebar {
            flex: 0 0 280px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            height: 100%; /* Ensure full height within main container */
            min-height: 0; /* IMPORTANT: Allow flex item to shrink */
        }

        .controls-sidebar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            flex-shrink: 0; /* Prevent header from shrinking */
        }

        .controls-sidebar-header h3 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .controls-sidebar-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .controls-sidebar-content {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow-y: auto;
            overflow-x: hidden;
            min-height: 0; /* Allow flex item to shrink */
            /* Remove the max-height calculation - let flex handle it */
            /* max-height: calc(100vh - 300px); */ /* REMOVE THIS LINE */
            scroll-behavior: smooth; /* Smooth scrolling */
            scrollbar-width: thin; /* Firefox */
            scrollbar-color: #667eea #f1f1f1; /* Firefox */
        }

        /* Webkit scrollbar styling for Chrome, Safari, Edge */
        .controls-sidebar-content::-webkit-scrollbar {
            width: 8px;
        }

        .controls-sidebar-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
            margin: 2px 0; /* Add margin to track */
        }

        .controls-sidebar-content::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 4px;
            transition: background 0.3s ease;
            min-height: 20px; /* Minimum thumb height */
        }

        .controls-sidebar-content::-webkit-scrollbar-thumb:hover {
            background: #5a6fd8;
        }

        .controls-sidebar-content::-webkit-scrollbar-thumb:active {
            background: #4c63d2;
        }

        .controls-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            flex-shrink: 0; /* Prevent sections from shrinking */
        }

        .controls-section-header {
            background: #e9ecef;
            padding: 12px 15px;
            font-weight: 600;
            font-size: 14px;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
        }

        .controls-section-content {
            padding: 12px;
        }

        .sidebar-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
            flex-shrink: 0; /* Prevent buttons from shrinking */
            padding: 15px; /* Add padding to match sidebar content */
            border-top: 1px solid #e9ecef; /* Visual separator from scrollable content */
            background: white; /* Ensure background matches */
        }

        /* Control group styling for sidebar */
        .controls-section .control-group {
            margin-bottom: 12px;
        }

        .controls-section .control-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 600;
            color: #495057;
            font-size: 12px;
        }

        .controls-section select,
        .controls-section input[type="range"] {
            width: 100%;
            padding: 6px 10px;
            border: 1px solid #ced4da;
            border-radius: 8px;
            background: white;
            font-size: 13px;
            color: #495057;
            box-sizing: border-box;
        }

        .controls-section select:focus,
        .controls-section input[type="range"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .compression-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .compression-control input[type="range"] {
            flex: 1;
        }

        .compression-control span {
            font-weight: 600;
            color: #667eea;
            min-width: 40px;
            text-align: right;
        }

        .preset-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .preset-btn {
            padding: 6px 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .clear-params-btn {
            padding: 6px 10px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            grid-column: span 2;
        }

        .clear-params-btn:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        /* Custom Agent Mode Styles */
        .custom-ruleset-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 120px;
            max-height: 300px;
            background: #f9f9f9;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .custom-ruleset-textarea:focus {
            outline: none;
            border-color: #667eea;
            background: white;
        }

        .character-counter {
            font-size: 11px;
            color: #6c757d;
            text-align: right;
            margin-top: 4px;
        }

        .character-counter.warning {
            color: #dc3545;
            font-weight: 600;
        }

        .apply-ruleset-btn {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            margin-right: 8px;
        }

        .apply-ruleset-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .clear-ruleset-btn {
            background: #e53e3e;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .clear-ruleset-btn:hover {
            background: #c53030;
            transform: translateY(-1px);
        }

        .ruleset-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 6px;
            font-size: 13px;
            display: none;
        }

        .ruleset-status.success {
            background: #c6f6d5;
            color: #276749;
            display: block;
        }

        .ruleset-status.error {
            background: #fed7d7;
            color: #9b2c2c;
            display: block;
        }

        .custom-agent-info {
            margin-bottom: 12px;
        }

        .custom-agent-info p {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
            line-height: 1.4;
        }

        .api-mode-info {
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }

        .api-mode-info p {
            font-size: 11px;
            color: #495057;
            margin: 0;
            line-height: 1.3;
        }

        .preset-rules {
            margin-bottom: 12px;
        }

        .preset-rules label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
            color: #495057;
            font-size: 12px;
        }

        .toggle-arrow {
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .controls-section-header {
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .controls-section-header h4 {
            margin: 0;
            font-size: 14px;
        }

        /* Update sidebar buttons to be full width */
        .sidebar-buttons .upload-btn,
        .sidebar-buttons .enhance-btn,
        .sidebar-buttons .send-btn {
            width: 100%;
            justify-content: center;
            padding: 12px;
            border-radius: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .sidebar-buttons .upload-btn span,
        .sidebar-buttons .enhance-btn span,
        .sidebar-buttons .send-btn span {
            font-size: 13px;
        }

        .image-gallery {
            flex: 0 0 260px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .gallery-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .gallery-header h3 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .gallery-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .gallery-content {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }

        .gallery-image {
            margin-bottom: 15px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .gallery-image:hover {
            transform: scale(1.02);
        }

        .gallery-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        .gallery-image-info {
            padding: 8px;
            background: #f8f9fa;
            font-size: 12px;
            color: #6c757d;
        }

        .no-images {
            text-align: center;
            color: #6c757d;
            padding: 20px;
            font-style: italic;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-bubble {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.agent .message-bubble {
            background: white;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .message-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: #667eea;
            order: 2;
        }

        .message.agent .message-avatar {
            background: #28a745;
        }

        .message-time {
            font-size: 11px;
            color: #6c757d;
            margin-top: 5px;
        }

        .message.user .message-time {
            text-align: right;
        }

        .generated-image {
            margin-top: 15px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .generated-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .chat-input {
            display: block;
        }

        .textarea-container {
            flex: 1;
            position: relative;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .textarea-container:focus-within {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .enhanced-textarea {
            width: 100%;
            min-height: 80px;
            max-height: 300px;
            padding: 20px 20px 40px 20px;
            border: none;
            outline: none;
            font-size: 16px;
            font-family: inherit;
            line-height: 1.5;
            resize: vertical;
            background: transparent;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .enhanced-textarea::placeholder {
            color: #6c757d;
            opacity: 0.8;
            line-height: 1.4;
        }

        .enhanced-textarea:focus::placeholder {
            opacity: 0.5;
        }

        .resize-indicator {
            position: absolute;
            bottom: 8px;
            right: 12px;
            color: #6c757d;
            opacity: 0.6;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .textarea-container:hover .resize-indicator {
            opacity: 1;
            color: #667eea;
        }

        .character-counter {
            position: absolute;
            bottom: 8px;
            left: 20px;
            font-size: 11px;
            color: #6c757d;
            opacity: 0.7;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .character-counter.warning {
            color: #ffc107;
            opacity: 1;
        }

        .character-counter.danger {
            color: #dc3545;
            opacity: 1;
            font-weight: 600;
        }

        .send-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 50px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .send-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .send-btn:active {
            transform: translateY(0);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .send-btn svg {
            transition: transform 0.3s ease;
        }

        .send-btn:hover:not(:disabled) svg {
            transform: translateX(2px);
        }

        /* Image upload button styles */
        .upload-btn {
            padding: 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 50px;
            min-width: 50px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            position: relative;
            overflow: hidden;
        }

        .upload-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .upload-btn:active {
            transform: translateY(0);
        }

        .upload-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .upload-btn input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .upload-btn svg {
            transition: transform 0.3s ease;
        }

        .upload-btn:hover:not(:disabled) svg {
            transform: scale(1.1);
        }

        /* Prompt enhancement button styles */
        .enhance-btn {
            padding: 15px;
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 50px;
            min-width: 50px;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
            position: relative;
            overflow: hidden;
        }

        .enhance-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
        }

        .enhance-btn:active {
            transform: translateY(0);
        }

        .enhance-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .enhance-btn svg {
            transition: transform 0.3s ease;
        }

        .enhance-btn:hover:not(:disabled) svg {
            transform: scale(1.1);
        }

        /* Uploaded images preview */
        .uploaded-images {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .uploaded-image {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 10px;
            overflow: hidden;
            border: 2px solid #e9ecef;
            background: #f8f9fa;
        }

        .uploaded-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .uploaded-image .remove-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .uploaded-image .remove-btn:hover {
            background: #c82333;
        }

        /* Auto-resize functionality */
        .enhanced-textarea.auto-resize {
            overflow-y: hidden;
        }

        /* Mobile responsive improvements */
        @media (max-width: 768px) {
            .enhanced-textarea {
                min-height: 60px;
                max-height: 200px;
                padding: 15px 15px 35px 15px;
                font-size: 16px; /* Prevent zoom on iOS */
            }

            .upload-btn {
                flex: 1;
                justify-content: center;
                padding: 15px;
                border-radius: 15px;
            }

            .enhance-btn {
                flex: 1;
                justify-content: center;
                padding: 15px;
                border-radius: 15px;
            }

            .send-btn {
                flex: 1;
                justify-content: center;
                padding: 15px;
                border-radius: 15px;
            }

            .uploaded-images {
                margin-bottom: 10px;
            }

            .uploaded-image {
                width: 60px;
                height: 60px;
            }

            .character-counter {
                left: 15px;
                bottom: 6px;
                font-size: 10px;
            }

            .resize-indicator {
                right: 10px;
                bottom: 6px;
            }
        }

        /* Smooth height transitions */
        @media (prefers-reduced-motion: no-preference) {
            .enhanced-textarea {
                transition: height 0.2s ease-out;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .textarea-container {
                border-width: 3px;
            }
            
            .character-counter {
                opacity: 1;
            }
        }

        .send-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .clear-btn {
            padding: 10px 15px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background: #c82333;
        }

        .typing-indicator {
            display: none;
            padding: 15px 20px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            border-bottom-left-radius: 5px;
            margin-bottom: 20px;
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #6c757d;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .status-bar {
            padding: 10px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Ultra-wide monitors - prevent over-stretching */
        @media (min-width: 2560px) {
            .main-container {
                max-width: 1600px;
                gap: 25px;
            }

            .controls-sidebar {
                flex: 0 0 350px;
            }

            .image-gallery {
                flex: 0 0 320px;
            }
        }

        /* Large desktop screens - optimize for very wide monitors */
        @media (min-width: 1920px) and (max-width: 2559px) {
            .main-container {
                max-width: 1400px;
                gap: 20px;
            }

            .controls-sidebar {
                flex: 0 0 320px;
            }

            .image-gallery {
                flex: 0 0 300px;
            }
        }

        /* Medium desktop screens - standard desktop optimization */
        @media (min-width: 1200px) and (max-width: 1919px) {
            .main-container {
                max-width: 1200px;
            }
        }

        /* Small desktop/large tablet - compact layout */
        @media (min-width: 1024px) and (max-width: 1199px) {
            .main-container {
                width: 100%;
                max-width: none;
                gap: 12px;
            }

            .controls-sidebar {
                flex: 0 0 260px;
            }

            .image-gallery {
                flex: 0 0 240px;
            }
        }

        /* Tablet landscape - more compact */
        @media (min-width: 769px) and (max-width: 1023px) {
            .main-container {
                width: 100%;
                max-width: none;
                gap: 10px;
                height: 95vh;
            }

            .controls-sidebar {
                flex: 0 0 240px;
                min-height: 0; /* Ensure this is set for smaller screens too */
            }

            .image-gallery {
                flex: 0 0 220px;
            }

            .controls-sidebar-content {
                padding: 12px;
                gap: 12px;
            }

            .controls-section-content {
                padding: 10px;
            }

            .controls-section .control-group {
                margin-bottom: 10px;
            }

            .sidebar-buttons {
                padding: 12px;
                gap: 10px;
            }

            /* Adjust scrollbar for smaller sidebar */
            .controls-sidebar-content::-webkit-scrollbar {
                width: 6px;
            }
        }

        /* Mobile portrait and small tablets */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                width: 100%;
                height: 100vh;
                gap: 0;
            }

            .image-gallery {
                width: 100%;
                height: 30%;
                border-radius: 0;
            }

            .chat-container {
                width: 100%;
                flex: 1;
                border-radius: 0;
            }

            .controls-sidebar {
                width: 100%;
                flex: 0 0 auto;
                border-radius: 0;
                order: -1; /* Show controls at top on mobile */
            }

            .controls-sidebar-content {
                padding: 15px;
                max-height: 200px; /* Set explicit height for mobile */
                overflow-y: auto;
                min-height: 0; /* Ensure proper shrinking on mobile */
            }

            .sidebar-buttons {
                padding: 10px 15px;
                gap: 8px;
            }

            /* Mobile scrollbar adjustments */
            .controls-sidebar-content::-webkit-scrollbar {
                width: 4px;
            }

            .controls-section {
                margin-bottom: 10px;
            }

            .sidebar-buttons {
                flex-direction: row;
                gap: 10px;
            }

            .message-bubble {
                max-width: 85%;
            }

            .gallery-content {
                display: flex;
                gap: 10px;
                padding: 10px;
                overflow-x: auto;
            }

            .gallery-image {
                min-width: 120px;
                margin-bottom: 0;
            }
        }

        /* Parameter Controls Styling */
        .parameter-controls {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
        }

        .controls-header h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
        }

        .toggle-controls {
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            padding: 0;
            transition: transform 0.3s ease;
        }

        .toggle-controls:hover {
            transform: scale(1.1);
        }

        .controls-content {
            padding: 20px;
            transition: all 0.3s ease;
        }

        .controls-content.collapsed {
            display: none;
        }

        .control-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            font-weight: 600;
            color: #495057;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .control-group select {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            transition: border-color 0.3s ease;
        }

        .control-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .compression-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .compression-control input[type="range"] {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #e9ecef;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .compression-control input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .compression-control input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        #compressionValue {
            font-size: 12px;
            font-weight: 600;
            color: #667eea;
            min-width: 40px;
            text-align: center;
        }

        .control-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .preset-btn, .clear-params-btn {
            padding: 6px 12px;
            border: 1px solid #667eea;
            border-radius: 20px;
            background: white;
            color: #667eea;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }

        .clear-params-btn {
            border-color: #dc3545;
            color: #dc3545;
        }

        .clear-params-btn:hover {
            background: #dc3545;
            color: white;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .control-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .control-actions {
                justify-content: center;
            }

            .preset-btn, .clear-params-btn {
                flex: 1;
                min-width: 120px;
            }
        }

        /* Enhancement Modal Styles */
        .enhancement-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .enhancement-modal {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            animation: slideIn 0.3s ease;
        }

        .enhancement-modal-header {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .enhancement-modal-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .enhancement-modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .enhancement-modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .enhancement-modal-content {
            padding: 20px;
            max-height: 50vh;
            overflow-y: auto;
        }

        .enhancement-section {
            margin-bottom: 20px;
        }

        .enhancement-section label {
            display: block;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .enhancement-original {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            color: #6c757d;
            line-height: 1.4;
        }

        .enhancement-enhanced {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 2px solid #ffc107;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
            line-height: 1.4;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .enhancement-enhanced:focus {
            outline: none;
            border-color: #ff8c00;
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
        }

        .enhancement-improvements {
            margin: 0;
            padding-left: 20px;
        }

        .enhancement-improvements li {
            margin-bottom: 5px;
            font-size: 14px;
            color: #495057;
        }

        .enhancement-modal-actions {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .enhancement-btn-primary,
        .enhancement-btn-secondary {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .enhancement-btn-primary {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
        }

        .enhancement-btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .enhancement-btn-secondary {
            background: #6c757d;
            color: white;
        }

        .enhancement-btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Force scrollbar visibility when debugging (temporary) */
.controls-sidebar-content.debug-scroll {
    overflow-y: scroll !important; /* Always show scrollbar for testing */
}

/* Ensure proper stacking context for scrollbar */
.controls-sidebar-content {
    position: relative; /* Create stacking context */
    z-index: 1; /* Ensure scrollbar is above content */
}

/* Add visual indicator when content is scrollable */
.controls-sidebar-content:not(:hover) {
    scrollbar-color: rgba(102, 126, 234, 0.5) #f1f1f1; /* Semi-transparent when not hovering */
}

.controls-sidebar-content:hover {
    scrollbar-color: #667eea #f1f1f1; /* Full opacity on hover */
}

    </style>
</head>
<body>
    <div class="main-container">
        <div class="image-gallery">
            <div class="gallery-header">
                <h3>🖼️ Image Gallery</h3>
                <p>Generated Images</p>
            </div>
            <div class="gallery-content" id="galleryContent">
                <div class="no-images">No images yet. Start generating!</div>
            </div>
        </div>

        <div class="chat-container">
        <div class="chat-header">
            <h1>🎨 AI Image Generation Agent</h1>
            <p>Powered by OpenAI's gpt-image-1 • Chat and create amazing images</p>
        </div>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>Agent Ready</span>
            </div>
            <button class="clear-btn" onclick="clearHistory()">Clear Chat</button>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message agent">
                <div class="message-avatar">🤖</div>
                <div>
                    <div class="message-bubble">
                        Hello! I'm your AI Image Generation Agent. I can help you create amazing images using the latest gpt-image-1 model. 
                        <br><br>
                        Try asking me to:
                        <br>• "Create an icon with transparent background"
                        <br>• "Generate a sunset landscape"  
                        <br>• "Make a logo for my business"
                        <br><br>
                        What would you like me to create for you?
                    </div>
                    <div class="message-time">Just now</div>
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input-container">
            <!-- Uploaded Images Preview -->
            <div class="uploaded-images" id="uploadedImages" style="display: none;"></div>

            <div class="chat-input">
                <div class="textarea-container">
                    <textarea
                        id="messageInput"
                        placeholder="Type your detailed image prompt here...&#10;&#10;💡 Tips:&#10;• Be specific about style, colors, composition&#10;• Mention lighting, mood, and atmosphere&#10;• Describe details like camera angle or art style&#10;• Use the controls panel on the right for settings&#10;• Upload reference images using the controls panel&#10;&#10;Drag the bottom-right corner to resize this text area"
                        onkeypress="handleKeyPress(event)"
                        rows="5"
                        class="enhanced-textarea"
                    ></textarea>
                    <div class="resize-indicator">
                        <svg width="12" height="12" viewBox="0 0 12 12">
                            <path d="M12 12L8 8M12 8L8 12M12 4L4 12" stroke="currentColor" stroke-width="1.5" fill="none"/>
                        </svg>
                    </div>
                    <div class="character-counter">
                        <span id="charCount">0</span> / 32,000 characters
                    </div>
                </div>
            </div>
        </div>
    </div> <!-- Close chat-container here -->

    <!-- Controls Sidebar -->
    <div class="controls-sidebar">
        <div class="controls-sidebar-header">
            <h3>🎛️ Controls</h3>
            <p>Image Parameters & Actions</p>
        </div>

        <div class="controls-sidebar-content">
            <!-- Image Parameters Section -->
            <div class="controls-section">
                <div class="controls-section-header">
                    🎨 Image Parameters
                </div>
                <div class="controls-section-content">
                    <div class="control-group">
                        <label>Output Format</label>
                        <select id="outputFormat">
                            <option value="">Auto</option>
                            <option value="png">PNG</option>
                            <option value="jpeg">JPEG</option>
                            <option value="webp">WebP</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Size</label>
                        <select id="imageSize">
                            <option value="">Auto</option>
                            <option value="1024x1024">Square (1024×1024)</option>
                            <option value="1536x1024">Landscape (1536×1024)</option>
                            <option value="1024x1536">Portrait (1024×1536)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Quality</label>
                        <select id="imageQuality">
                            <option value="">Auto</option>
                            <option value="low">Low (Fast)</option>
                            <option value="medium">Medium</option>
                            <option value="high">High (Best)</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Background</label>
                        <select id="imageBackground">
                            <option value="">Auto</option>
                            <option value="opaque">Opaque</option>
                            <option value="transparent">Transparent</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Compression (%)</label>
                        <div class="compression-control">
                            <input type="range" id="outputCompression" min="10" max="100" value="100" step="10">
                            <span id="compressionValue">100%</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>Input Fidelity</label>
                        <select id="inputFidelity">
                            <option value="low">Low</option>
                            <option value="high">High</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label>Show AI-Optimized Prompt</label>
                        <select id="showRevisedPrompt">
                            <option value="false">No (Default)</option>
                            <option value="true">Yes</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- API Mode Selection Section -->
            <div class="controls-section">
                <div class="controls-section-header">
                    🔧 API Mode
                </div>
                <div class="controls-section-content">
                    <div class="control-group">
                        <label>Image Generation API</label>
                        <select id="apiMode">
                            <option value="responses">Responses API (Default)</option>
                            <option value="legacy">Legacy API (No Auto-Enhancement)</option>
                        </select>
                    </div>
                    <div class="api-mode-info" id="apiModeInfo">
                        <p><strong>Responses API:</strong> Advanced features with automatic prompt optimization, multi-turn editing, and streaming support.</p>
                    </div>
                </div>
            </div>

            <!-- Quick Presets Section -->
            <div class="controls-section">
                <div class="controls-section-header">
                    ⚡ Quick Presets
                </div>
                <div class="controls-section-content">
                    <div class="preset-buttons">
                        <button class="preset-btn" onclick="applyPreset('logo')">📱 Logo</button>
                        <button class="preset-btn" onclick="applyPreset('photo')">📸 Photo</button>
                        <button class="preset-btn" onclick="applyPreset('fast')">⚡ Fast</button>
                        <button class="clear-params-btn" onclick="clearParameters()">🗑️ Clear</button>
                    </div>
                </div>
            </div>

            <!-- Custom Agent Mode Section -->
            <div class="controls-section">
                <div class="controls-section-header" onclick="toggleCustomAgentMode()">
                    <h4>🤖 Custom Agent Mode</h4>
                    <span class="toggle-arrow" id="customAgentArrow">▼</span>
                </div>
                <div class="controls-section-content" id="customAgentContent">
                    <div class="custom-agent-info">
                        <p>Define custom instructions for the agent to follow. These will override default behaviors.</p>
                    </div>

                    <div class="control-group">
                        <label for="customRuleset">Custom Ruleset:</label>
                        <textarea
                            id="customRuleset"
                            class="custom-ruleset-textarea"
                            placeholder="Enter custom instructions for the agent...&#10;Example:&#10;- Always suggest vibrant colors&#10;- Focus on photorealistic style&#10;- Include nature elements when possible"
                            maxlength="2000"
                            rows="8"
                        ></textarea>
                        <div class="character-counter" id="rulesetCounter">0 / 2000</div>
                    </div>

                    <div class="preset-rules">
                        <label>Quick Presets:</label>
                        <div class="preset-buttons">
                            <button class="preset-btn" onclick="applyRulesetPreset('artistic')">🎨 Artistic</button>
                            <button class="preset-btn" onclick="applyRulesetPreset('technical')">⚙️ Technical</button>
                            <button class="preset-btn" onclick="applyRulesetPreset('minimal')">✨ Minimal</button>
                        </div>
                    </div>

                    <div class="control-actions">
                        <button class="apply-ruleset-btn" onclick="applyCustomRuleset()">Apply Ruleset</button>
                        <button class="clear-ruleset-btn" onclick="clearCustomRuleset()">Clear</button>
                    </div>

                    <div class="ruleset-status" id="rulesetStatus"></div>
                </div>
            </div>
        </div>

        <!-- Action Buttons - Outside scrollable area -->
        <div class="sidebar-buttons">
            <button class="upload-btn" title="Upload reference images">
                <input type="file" id="imageUpload" accept="image/*" multiple onchange="handleImageUpload(event)">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    <path d="M12,12L16,16H13V19H11V16H8L12,12Z"/>
                </svg>
                <span>Upload Images</span>
            </button>

            <button class="enhance-btn" onclick="enhancePrompt()" id="enhanceBtn" title="Enhance your prompt with AI">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A7,7 0 0,1 19,9C19,11.38 17.81,13.47 16,14.74V17A1,1 0 0,1 15,18H9A1,1 0 0,1 8,17V14.74C6.19,13.47 5,11.38 5,9A7,7 0 0,1 12,2M9,21V20H15V21A1,1 0 0,1 14,22H10A1,1 0 0,1 9,21M12,4A5,5 0 0,0 7,9C7,11.05 8.23,12.81 10,13.58V16H14V13.58C15.77,12.81 17,11.05 17,9A5,5 0 0,0 12,4Z"/>
                </svg>
                <span>Enhance Prompt</span>
            </button>

            <button class="send-btn" onclick="sendMessage()" id="sendBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
                <span>Send Message</span>
            </button>
        </div>
    </div>
</div> <!-- Close main-container -->
    <script>
        let isLoading = false;

        function formatTime(date) {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        }

        function addMessage(content, isUser, images = []) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'agent'}`;

            const avatar = isUser ? '👤' : '🤖';
            const time = formatTime(new Date());

            let imageHtml = '';
            if (images && images.length > 0) {
                imageHtml = images.map(img => 
                    `<div class="generated-image">
                        <img src="${img}" alt="Generated image" onclick="window.open('${img}', '_blank')">
                    </div>`
                ).join('');
            }

            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div>
                    <div class="message-bubble">
                        ${content.replace(/\n/g, '<br>')}
                        ${imageHtml}
                    </div>
                    <div class="message-time">${time}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTyping() {
            const indicator = document.getElementById('typingIndicator');
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.appendChild(indicator);
            indicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTyping() {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = 'none';
        }

        // Parameter Controls JavaScript
        let controlsCollapsed = false;

        function toggleControls() {
            const content = document.getElementById('controlsContent');
            const icon = document.getElementById('toggleIcon');
            
            controlsCollapsed = !controlsCollapsed;
            
            if (controlsCollapsed) {
                content.classList.add('collapsed');
                icon.textContent = '▶';
            } else {
                content.classList.remove('collapsed');
                icon.textContent = '▼';
            }
        }

        // Update compression value display
        document.getElementById('outputCompression').addEventListener('input', function() {
            document.getElementById('compressionValue').textContent = this.value + '%';
        });

        // Preset configurations
        function applyPreset(type) {
            const presets = {
                logo: {
                    outputFormat: 'png',
                    imageSize: '1024x1024',
                    imageQuality: 'high',
                    imageBackground: 'transparent',
                    outputCompression: 100,
                    inputFidelity: 'high'
                },
                photo: {
                    outputFormat: 'jpeg',
                    imageSize: '1536x1024',
                    imageQuality: 'high',
                    imageBackground: 'opaque',
                    outputCompression: 90,
                    inputFidelity: 'high'
                },
                fast: {
                    outputFormat: 'jpeg',
                    imageSize: '1024x1024',
                    imageQuality: 'low',
                    imageBackground: 'opaque',
                    outputCompression: 70,
                    inputFidelity: 'low'
                }
            };

            const preset = presets[type];
            if (preset) {
                Object.keys(preset).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = preset[key];
                        if (key === 'outputCompression') {
                            document.getElementById('compressionValue').textContent = preset[key] + '%';
                        }
                    }
                });
            }
        }

        function clearParameters() {
            document.getElementById('outputFormat').value = '';
            document.getElementById('imageSize').value = '';
            document.getElementById('imageQuality').value = '';
            document.getElementById('imageBackground').value = '';
            document.getElementById('outputCompression').value = 100;
            document.getElementById('compressionValue').textContent = '100%';
            document.getElementById('inputFidelity').value = 'low';
            document.getElementById('showRevisedPrompt').value = 'false';
            document.getElementById('apiMode').value = 'responses';
            updateApiModeInfo();
        }

        function getSelectedParameters() {
            const params = {};

            const outputFormat = document.getElementById('outputFormat').value;
            const imageSize = document.getElementById('imageSize').value;
            const imageQuality = document.getElementById('imageQuality').value;
            const imageBackground = document.getElementById('imageBackground').value;
            const outputCompression = document.getElementById('outputCompression').value;
            const inputFidelity = document.getElementById('inputFidelity').value;
            const showRevisedPrompt = document.getElementById('showRevisedPrompt').value;
            const apiMode = document.getElementById('apiMode').value;

            if (outputFormat) params.output_format = outputFormat;
            if (imageSize) params.size = imageSize;
            if (imageQuality) params.quality = imageQuality;
            if (imageBackground) params.background = imageBackground;
            if (outputCompression !== '100') params.output_compression = parseInt(outputCompression);
            if (inputFidelity !== 'low') params.input_fidelity = inputFidelity;
            if (showRevisedPrompt === 'true') params.show_revised_prompt = true;
            if (apiMode !== 'responses') params.api_mode = apiMode;

            return params;
        }

        function buildParameterString() {
            const params = getSelectedParameters();
            if (Object.keys(params).length === 0) return '';

            const paramStrings = [];
            Object.entries(params).forEach(([key, value]) => {
                paramStrings.push(`${key}=${value}`);
            });

            return ` [Parameters: ${paramStrings.join(', ')}]`;
        }

        function updateApiModeInfo() {
            const apiMode = document.getElementById('apiMode').value;
            const infoElement = document.getElementById('apiModeInfo');

            if (apiMode === 'responses') {
                infoElement.innerHTML = '<p><strong>Responses API:</strong> Advanced features with automatic prompt optimization, multi-turn editing, and streaming support.</p>';
            } else {
                infoElement.innerHTML = '<p><strong>Legacy API:</strong> Direct image generation with exact prompt preservation. No automatic enhancement or multi-turn features.</p>';
            }
        }

        // Enhanced textarea functionality
        let userPreferredHeight = null;
        const STORAGE_KEY = 'chatTextareaHeight';

        function initializeTextarea() {
            const textarea = document.getElementById('messageInput');
            const charCount = document.getElementById('charCount');
            
            // Load saved height preference
            const savedHeight = localStorage.getItem(STORAGE_KEY);
            if (savedHeight) {
                textarea.style.height = savedHeight;
                userPreferredHeight = parseInt(savedHeight);
            }

            // Character counter
            textarea.addEventListener('input', function() {
                const length = this.value.length;
                charCount.textContent = length.toLocaleString();
                
                // Update counter styling based on length
                const counter = charCount.parentElement;
                counter.classList.remove('warning', 'danger');
                
                if (length > 28000) {
                    counter.classList.add('danger');
                } else if (length > 24000) {
                    counter.classList.add('warning');
                }

                // Auto-resize functionality
                autoResizeTextarea(this);
            });

            // Save height preference when user resizes
            const resizeObserver = new ResizeObserver(entries => {
                for (let entry of entries) {
                    const height = entry.target.style.height || entry.target.offsetHeight + 'px';
                    if (height !== userPreferredHeight) {
                        userPreferredHeight = height;
                        localStorage.setItem(STORAGE_KEY, height);
                    }
                }
            });

            resizeObserver.observe(textarea);

            // Prevent textarea from shrinking below minimum on manual resize
            textarea.addEventListener('mouseup', function() {
                const currentHeight = parseInt(this.style.height || this.offsetHeight);
                if (currentHeight < 80) {
                    this.style.height = '80px';
                }
                if (currentHeight > 300) {
                    this.style.height = '300px';
                }
            });

            // Enhanced keyboard shortcuts
            textarea.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + Enter to send
                if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                    e.preventDefault();
                    sendMessage();
                }
                
                // Ctrl/Cmd + / to focus parameter controls
                if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                    e.preventDefault();
                    const firstSelect = document.querySelector('#parameterControls select');
                    if (firstSelect) firstSelect.focus();
                }
            });

            // Initial character count
            charCount.textContent = '0';
        }

        function autoResizeTextarea(textarea) {
            // Auto-resize based on content (optional enhancement)
            if (textarea.scrollHeight > textarea.clientHeight && textarea.scrollHeight <= 300) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 300) + 'px';
            }
        }

        function resetTextareaHeight() {
            const textarea = document.getElementById('messageInput');
            textarea.style.height = '80px';
            localStorage.removeItem(STORAGE_KEY);
            userPreferredHeight = null;
        }

        // Enhanced sendMessage function with better UX
        async function sendMessage() {
            if (isLoading) return;

            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            let message = input.value.trim();

            if (!message) {
                // Shake animation for empty input
                input.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => input.style.animation = '', 500);
                return;
            }

            // Check character limit
            if (message.length > 32000) {
                addMessage('⚠️ Message too long. Please keep it under 32,000 characters.', false);
                return;
            }

            // Get selected parameters
            const parameters = getSelectedParameters();
            
            // Add user message with uploaded images
            const userImages = uploadedImages.map(img => img.dataUrl);
            addMessage(message, true, userImages);

            // Clear uploaded images after sending
            clearUploadedImages();
            
            // Clear input and reset height if auto-resize is enabled
            input.value = '';
            document.getElementById('charCount').textContent = '0';
            
            // Reset to preferred height or default
            if (userPreferredHeight) {
                input.style.height = userPreferredHeight;
            } else {
                input.style.height = '80px';
            }

            // Show loading state with enhanced feedback
            isLoading = true;
            sendBtn.disabled = true;
            sendBtn.innerHTML = `
                <div class="spinner"></div>
                <span>Generating...</span>
            `;
            showTyping();

            try {
                const requestBody = {
                    message: message,
                    parameters: parameters
                };

                // Add uploaded images if any
                if (uploadedImages.length > 0) {
                    requestBody.images = uploadedImages.map(img => ({
                        name: img.name,
                        base64: img.base64,
                        dataUrl: img.dataUrl
                    }));
                }

                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody),
                });

                const data = await response.json();

                if (response.ok) {
                    let responseMessage = data.response;

                    // Add information about processed images if any
                    if (data.processed_images_count > 0) {
                        responseMessage += `\n\n📎 Successfully processed ${data.processed_images_count} uploaded image(s) for use in generation and editing.`;
                    }

                    // Add any image processing errors
                    if (data.image_processing_errors && data.image_processing_errors.length > 0) {
                        responseMessage += `\n\n⚠️ Image processing warnings:\n${data.image_processing_errors.join('\n')}`;
                    }

                    addMessage(responseMessage, false, data.images || []);
                } else {
                    addMessage(`❌ Error: ${data.error}`, false);
                }
            } catch (error) {
                addMessage(`🔌 Connection error: ${error.message}`, false);
            } finally {
                isLoading = false;
                sendBtn.disabled = false;
                sendBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                    <span>Send</span>
                `;
                hideTyping();
                
                // Refocus textarea for continued conversation
                input.focus();
            }
        }

        // Add shake animation CSS
        const shakeCSS = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
            
            .spinner {
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255,255,255,0.3);
                border-top: 2px solid white;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        
        // Inject additional CSS
        const style = document.createElement('style');
        style.textContent = shakeCSS;
        document.head.appendChild(style);

        // Image upload functionality
        let uploadedImages = [];

        function handleImageUpload(event) {
            const files = Array.from(event.target.files);

            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imageData = {
                            file: file,
                            dataUrl: e.target.result,
                            base64: e.target.result.split(',')[1],
                            name: file.name
                        };

                        uploadedImages.push(imageData);
                        updateImagePreview();
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Clear the input so the same file can be uploaded again
            event.target.value = '';
        }

        function updateImagePreview() {
            const container = document.getElementById('uploadedImages');

            if (uploadedImages.length === 0) {
                container.style.display = 'none';
                container.innerHTML = '';
                return;
            }

            container.style.display = 'flex';
            container.innerHTML = uploadedImages.map((img, index) => `
                <div class="uploaded-image">
                    <img src="${img.dataUrl}" alt="${img.name}" title="${img.name}">
                    <button class="remove-btn" onclick="removeUploadedImage(${index})" title="Remove image">×</button>
                </div>
            `).join('');
        }

        function removeUploadedImage(index) {
            uploadedImages.splice(index, 1);
            updateImagePreview();
        }

        function clearUploadedImages() {
            uploadedImages = [];
            updateImagePreview();
        }

        // Initialize compression slider
        function initializeCompressionSlider() {
            const slider = document.getElementById('outputCompression');
            const valueDisplay = document.getElementById('compressionValue');

            if (slider && valueDisplay) {
                slider.addEventListener('input', function() {
                    valueDisplay.textContent = this.value + '%';
                });
            }
        }

        // Custom Agent Mode Management
        let customRuleset = '';
        let customAgentModeCollapsed = false;

        function toggleCustomAgentMode() {
            customAgentModeCollapsed = !customAgentModeCollapsed;
            const content = document.getElementById('customAgentContent');
            const arrow = document.getElementById('customAgentArrow');

            if (customAgentModeCollapsed) {
                content.style.display = 'none';
                arrow.textContent = '▶';
            } else {
                content.style.display = 'block';
                arrow.textContent = '▼';
            }
        }

        // Ruleset presets
        const rulesetPresets = {
            artistic: `You are an artistic image generation assistant. Follow these rules:
- Prioritize artistic expression and creativity
- Suggest bold color palettes and dynamic compositions
- Include artistic movements or styles in your recommendations
- Focus on emotional impact and visual storytelling
- Always mention potential artistic techniques that could enhance the image`,

            technical: `You are a technical image generation assistant. Follow these rules:
- Focus on technical accuracy and precision
- Provide detailed specifications for image parameters
- Suggest optimal resolutions and aspect ratios
- Include technical considerations like lighting and perspective
- Recommend settings for maximum quality output`,

            minimal: `You are a minimalist image generation assistant. Follow these rules:
- Emphasize simplicity and clean aesthetics
- Suggest limited color palettes (2-3 colors maximum)
- Focus on negative space and composition
- Avoid cluttered or complex scenes
- Recommend minimal, essential elements only`
        };

        function applyRulesetPreset(type) {
            const textarea = document.getElementById('customRuleset');
            textarea.value = rulesetPresets[type] || '';
            textarea.dispatchEvent(new Event('input')); // Trigger character counter
        }

        async function applyCustomRuleset() {
            const ruleset = document.getElementById('customRuleset').value.trim();
            const statusDiv = document.getElementById('rulesetStatus');

            if (!ruleset) {
                showRulesetStatus('Please enter a custom ruleset', 'error');
                return;
            }

            try {
                const response = await fetch('/api/set-custom-ruleset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ ruleset })
                });

                const data = await response.json();

                if (response.ok) {
                    customRuleset = ruleset;
                    showRulesetStatus('✅ Custom ruleset applied successfully', 'success');
                } else {
                    showRulesetStatus(`❌ Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showRulesetStatus(`❌ Failed to apply ruleset: ${error.message}`, 'error');
            }
        }

        async function clearCustomRuleset() {
            try {
                const response = await fetch('/api/clear-custom-ruleset', {
                    method: 'POST'
                });

                if (response.ok) {
                    document.getElementById('customRuleset').value = '';
                    document.getElementById('rulesetCounter').textContent = '0 / 2000';
                    customRuleset = '';
                    showRulesetStatus('✅ Custom ruleset cleared', 'success');
                }
            } catch (error) {
                showRulesetStatus(`❌ Failed to clear ruleset: ${error.message}`, 'error');
            }
        }

        function showRulesetStatus(message, type) {
            const statusDiv = document.getElementById('rulesetStatus');
            statusDiv.textContent = message;
            statusDiv.className = `ruleset-status ${type}`;

            setTimeout(() => {
                statusDiv.className = 'ruleset-status';
            }, 5000);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeTextarea();
            initializeCompressionSlider();
            document.getElementById('messageInput').focus();
            loadGalleryImages();

            // Initialize API mode selector
            const apiModeSelect = document.getElementById('apiMode');
            if (apiModeSelect) {
                updateApiModeInfo(); // Set initial info
                apiModeSelect.addEventListener('change', updateApiModeInfo);
            }

            // Initialize character counter for custom ruleset
            const rulesetTextarea = document.getElementById('customRuleset');
            if (rulesetTextarea) {
                rulesetTextarea.addEventListener('input', function() {
                    const length = this.value.length;
                    const counter = document.getElementById('rulesetCounter');
                    counter.textContent = `${length} / 2000`;

                    if (length > 1800) {
                        counter.classList.add('warning');
                    } else {
                        counter.classList.remove('warning');
                    }
                });
            }
        });

        function handleKeyPress(event) {
            // Allow Enter key to create new lines
            // Ctrl/Cmd + Enter to send
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
            return true;
        }

        async function clearHistory() {
            try {
                await fetch('/api/clear', { method: 'POST' });
                document.getElementById('chatMessages').innerHTML = '';
                addMessage('Chat history has been cleared. How can I help you today?', false);
            } catch (error) {
                console.error('Error clearing history:', error);
            }
        }

        // Gallery functions
        async function loadGalleryImages() {
            try {
                const response = await fetch('/api/images');
                const data = await response.json();
                displayGalleryImages(data.images || []);
            } catch (error) {
                console.error('Error loading gallery images:', error);
            }
        }

        function displayGalleryImages(images) {
            const galleryContent = document.getElementById('galleryContent');
            
            if (images.length === 0) {
                galleryContent.innerHTML = '<div class="no-images">No images yet. Start generating!</div>';
                return;
            }

            galleryContent.innerHTML = images.map(img => `
                <div class="gallery-image" onclick="window.open('${img.url}', '_blank')">
                    <img src="${img.url}" alt="Generated image" loading="lazy">
                    <div class="gallery-image-info">
                        ${formatFileSize(img.size)}<br>
                        ${formatDate(img.created)}
                    </div>
                </div>
            `).join('');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString([], {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Refresh gallery after successful image generation
        const originalSendMessage = sendMessage;
        sendMessage = async function() {
            await originalSendMessage();
            setTimeout(loadGalleryImages, 1000); // Refresh gallery after 1 second
        };

        // Prompt Enhancement Functionality
        let isEnhancing = false;

        async function enhancePrompt() {
            if (isEnhancing) return;

            const input = document.getElementById('messageInput');
            const enhanceBtn = document.getElementById('enhanceBtn');
            const originalPrompt = input.value.trim();

            if (!originalPrompt) {
                // Shake animation for empty input
                input.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => input.style.animation = '', 500);
                return;
            }

            // Show loading state
            isEnhancing = true;
            enhanceBtn.disabled = true;
            enhanceBtn.innerHTML = `
                <div class="spinner"></div>
                <span>Enhancing...</span>
            `;

            try {
                const response = await fetch('/api/enhance-prompt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: originalPrompt,
                        enhancement_level: 'moderate'
                    }),
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    showEnhancementModal(data);
                } else {
                    addMessage(`❌ Enhancement error: ${data.error || 'Unknown error'}`, false);
                }
            } catch (error) {
                addMessage(`🔌 Enhancement connection error: ${error.message}`, false);
            } finally {
                isEnhancing = false;
                enhanceBtn.disabled = false;
                enhanceBtn.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2A7,7 0 0,1 19,9C19,11.38 17.81,13.47 16,14.74V17A1,1 0 0,1 15,18H9A1,1 0 0,1 8,17V14.74C6.19,13.47 5,11.38 5,9A7,7 0 0,1 12,2M9,21V20H15V21A1,1 0 0,1 14,22H10A1,1 0 0,1 9,21M12,4A5,5 0 0,0 7,9C7,11.05 8.23,12.81 10,13.58V16H14V13.58C15.77,12.81 17,11.05 17,9A5,5 0 0,0 12,4Z"/>
                    </svg>
                    <span>Enhance Prompt</span>
                `;
            }
        }

        function showEnhancementModal(data) {
            // Create modal overlay
            const modal = document.createElement('div');
            modal.className = 'enhancement-modal-overlay';
            modal.innerHTML = `
                <div class="enhancement-modal">
                    <div class="enhancement-modal-header">
                        <h3>✨ Enhanced Prompt</h3>
                        <button class="enhancement-modal-close" onclick="closeEnhancementModal()">&times;</button>
                    </div>
                    <div class="enhancement-modal-content">
                        <div class="enhancement-section">
                            <label>Original Prompt:</label>
                            <div class="enhancement-original">${data.original_prompt}</div>
                        </div>
                        <div class="enhancement-section">
                            <label>Enhanced Prompt:</label>
                            <textarea class="enhancement-enhanced" id="enhancedPromptText">${data.enhanced_prompt}</textarea>
                        </div>
                        ${data.improvements ? `
                        <div class="enhancement-section">
                            <label>Improvements Made:</label>
                            <ul class="enhancement-improvements">
                                ${data.improvements.map(imp => `<li>${imp}</li>`).join('')}
                            </ul>
                        </div>
                        ` : ''}
                    </div>
                    <div class="enhancement-modal-actions">
                        <button class="enhancement-btn-secondary" onclick="closeEnhancementModal()">Cancel</button>
                        <button class="enhancement-btn-primary" onclick="acceptEnhancement()">Use Enhanced Prompt</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Focus on the enhanced prompt textarea for editing
            setTimeout(() => {
                document.getElementById('enhancedPromptText').focus();
            }, 100);
        }

        function closeEnhancementModal() {
            const modal = document.querySelector('.enhancement-modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        function acceptEnhancement() {
            const enhancedText = document.getElementById('enhancedPromptText').value;
            const input = document.getElementById('messageInput');

            input.value = enhancedText;
            input.focus();

            // Trigger input event to update character counter
            input.dispatchEvent(new Event('input'));

            closeEnhancementModal();
        }
    </script>
</body>
</html>
