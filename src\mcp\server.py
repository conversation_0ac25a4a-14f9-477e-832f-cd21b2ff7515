"""
Starlette + MCP SSE transport server (Option A: tools-only server).

This server wires the Model Context Protocol Python server with Streamable HTTP (SSE),
exposing your image tools and generated image resources for IDE MCP clients.

Requires a Python MCP SDK that provides `mcp.server.Server`, `mcp.types`, and `mcp.server.sse.SseServerTransport`.
"""
from typing import Any, Dict, List
import base64
import os
from urllib.parse import urlparse
from starlette.applications import Starlette
from starlette.responses import JSONResponse
from starlette.routing import Route, Mount
import contextlib
from starlette.requests import Request

try:
    from mcp.server import Server  # type: ignore
    import mcp.types as types  # type: ignore
    from mcp.server.sse import SseServerTransport  # type: ignore
    # from mcp.server.streamable_http_manager import StreamableHTTPSessionManager  # type: ignore
except Exception as exc:  # pragma: no cover
    raise RuntimeError(
        "MCP Python SDK not installed. Please install a package that provides 'mcp.server' and 'mcp.server.sse'."
    ) from exc

from . import tools as tool_adapters
from . import resources as resource_adapters


async def health(_request: Request):
    return JSONResponse({"status": "ok", "server": "mcp-sse"})


# Create MCP server
app = Server("image-mcp-server")


@app.list_tools()
async def mcp_list_tools() -> List[types.Tool]:  # type: ignore[name-defined]
    tool_defs = tool_adapters.list_tools()
    tools: List[types.Tool] = []
    for td in tool_defs:
        tools.append(
            types.Tool(
                name=td["name"],
                description=td.get("description", ""),
                inputSchema=td.get("inputSchema", {"type": "object"})
            )
        )
    return tools


def _to_types_content(items: List[Dict[str, Any]]):
    out: List[Any] = []
    for it in items:
        t = it.get("type")
        if t == "text":
            out.append(types.TextContent(type="text", text=it.get("text", "")))
        elif t == "resource" and isinstance(it.get("resource"), dict):
            res = dict(it["resource"])  # copy
            # If neither text nor blob provided, and file:// URI is available, embed blob for compatibility
            if (not res.get("text") and not res.get("blob")) and isinstance(res.get("uri"), str) and res["uri"].startswith("file://"):
                parsed = urlparse(res["uri"])
                path = parsed.path
                if os.name == 'nt' and path.startswith('/'):
                    path = path.lstrip('/')
                try:
                    with open(path, 'rb') as f:
                        res["blob"] = base64.b64encode(f.read()).decode('utf-8')
                except Exception:
                    # If we cannot read, leave as-is; client may fetch via read_resource
                    pass
            out.append(
                types.EmbeddedResource(
                    type="resource",
                    resource={k: v for k, v in res.items() if v is not None},
                )
            )
        elif "uri" in it:
            # Back-compat for simple resource dicts -> wrap into EmbeddedResource shape
            res = {
                "uri": it.get("uri"),
                "mimeType": it.get("mimeType"),
                "text": it.get("text"),
                "blob": it.get("blob"),
            }
            if (not res.get("text") and not res.get("blob")) and isinstance(res.get("uri"), str) and res["uri"].startswith("file://"):
                parsed = urlparse(res["uri"])
                path = parsed.path
                if os.name == 'nt' and path.startswith('/'):
                    path = path.lstrip('/')
                try:
                    with open(path, 'rb') as f:
                        res["blob"] = base64.b64encode(f.read()).decode('utf-8')
                except Exception:
                    pass
            out.append(
                types.EmbeddedResource(
                    type="resource",
                    resource={k: v for k, v in res.items() if v is not None},
                )
            )
        else:
            # Fallback stringify
            out.append(types.TextContent(type="text", text=str(it)))
    return out


@app.call_tool()
async def mcp_call_tool(name: str, arguments: Dict[str, Any]) -> List[Any]:
    if name == "image_edit_bfl_kontext":
        content = tool_adapters.call_image_edit_bfl_kontext(arguments)
    elif name == "image_generate_bfl_kontext":
        content = tool_adapters.call_image_generate_bfl_kontext(arguments)
    elif name == "image_generate_responses":  # deprecated shims
        content = tool_adapters.call_image_generate_responses(arguments)
    elif name == "image_edit_responses":
        content = tool_adapters.call_image_edit_responses(arguments)
    elif name == "image_edit_mask_responses":
        content = tool_adapters.call_image_edit_mask_responses(arguments)
    else:
        return [types.TextContent(type="text", text=f"Tool not found: {name}")]
    return _to_types_content(content)


@app.list_resources()
async def mcp_list_resources() -> List[types.Resource]:  # type: ignore[name-defined]
    res_payload = resource_adapters.list_resources()
    res_list: List[types.Resource] = []
    for r in res_payload.get("resources", []):
        res_list.append(
            types.Resource(
                uri=r.get("uri"),
                name=r.get("name"),
                description=r.get("description"),
                mimeType=r.get("mimeType"),
            )
        )
    return res_list


@app.read_resource()
async def mcp_read_resource(uri: str) -> types.ReadResourceResult:  # type: ignore[name-defined]
    payload = resource_adapters.read_resource(uri)
    contents = []
    for c in payload.get("contents", []):
        contents.append(
            types.ResourceContents(
                uri=c.get("uri"),
                mimeType=c.get("mimeType"),
                text=c.get("text"),
                blob=c.get("blob"),
            )
        )
    return types.ReadResourceResult(contents=contents)


# Wire SSE transport endpoints
# Some MCP clients POST to "/" and others to a dedicated "/messages" path.
# We support both by creating two transports bound to each path.
sse_root = SseServerTransport("/")
sse_messages = SseServerTransport("/messages")
sse_sse = SseServerTransport("/sse")


async def handle_sse_root(scope, receive, send):
    async with sse_root.connect_sse(scope, receive, send) as streams:
        await app.run(streams[0], streams[1], app.create_initialization_options())


async def handle_messages_root(scope, receive, send):
    # Accept POST messages to the root path
    await sse_root.handle_post_message(scope, receive, send)


async def handle_messages_messages(scope, receive, send):
    # Also accept POST messages on /messages for wider compatibility
    await sse_messages.handle_post_message(scope, receive, send)


async def handle_sse_sse(scope, receive, send):
    # Legacy SSE endpoint at /sse
    async with sse_sse.connect_sse(scope, receive, send) as streams:
        await app.run(streams[0], streams[1], app.create_initialization_options())


# Deprecated: earlier Route/ASGIRoute wiring removed in favor of Mount-based ASGI apps

# --- Streamable HTTP transport temporarily disabled (SSE-only mode) ---
@contextlib.asynccontextmanager
async def lifespan(_app: Starlette):
    # No streamable HTTP session manager running; SSE only
    yield


# Rebuild Starlette app with both health + streamable HTTP + SSE compatibility
async def root_asgi(scope, receive, send):
    if scope.get("type") != "http":
        return await JSONResponse({"error": "Unsupported scope"}, status_code=404)(scope, receive, send)
    method = scope.get("method", "GET").upper()
    if method == "GET":
        return await handle_sse_root(scope, receive, send)
    if method == "POST":
        return await sse_root.handle_post_message(scope, receive, send)
    return await JSONResponse({"error": "Method not allowed"}, status_code=405)(scope, receive, send)


async def sse_only_asgi(scope, receive, send):
    if scope.get("type") != "http":
        return await JSONResponse({"error": "Unsupported scope"}, status_code=404)(scope, receive, send)
    if scope.get("method", "GET").upper() != "GET":
        return await JSONResponse({"error": "Method not allowed"}, status_code=405)(scope, receive, send)
    return await handle_sse_sse(scope, receive, send)


async def messages_only_asgi(scope, receive, send):
    if scope.get("type") != "http":
        return await JSONResponse({"error": "Unsupported scope"}, status_code=404)(scope, receive, send)
    if scope.get("method", "POST").upper() != "POST":
        return await JSONResponse({"error": "Method not allowed"}, status_code=405)(scope, receive, send)
    return await sse_messages.handle_post_message(scope, receive, send)


starlette_app = Starlette(
    routes=[
        # Health
        Route("/api/health", health, methods=["GET"]),
        # Mount ASGI apps to ensure correct ASGI handling for SSE and POST messages
        Mount("/", app=root_asgi),
        Mount("/sse", app=sse_only_asgi),
        Mount("/messages", app=messages_only_asgi),
        # Alias for clients configured to POST JSON-RPC to /mcp
        Mount("/mcp", app=messages_only_asgi),
    ],
    lifespan=lifespan,
)
